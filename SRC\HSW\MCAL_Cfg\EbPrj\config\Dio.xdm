<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Dio" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Dio" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Dio"/>
              <a:a name="IMPORTER_INFO" value="SplittableImp_Rte"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="DioConfig" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="SplittableImp_Rte"/>
                <d:lst name="DioPort" type="MAP">
                  <d:ctr name="DioPort_PA" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="0">
                      <a:a name="CUSTOMDATA" type="CUSTOMDATA">
                        <cd:v id="splittag">EB.S32G399.COMMON</cd:v>
                      </a:a>
                    </d:var>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PA_6_CCU_LIN3_SLP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="6">
                          <a:a name="CUSTOMDATA" type="CUSTOMDATA">
                            <cd:v id="splittag">EB.S32G399.COMMON</cd:v>
                          </a:a>
                        </d:var>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PA_7_CCU_LIN4_SLP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PA_8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PA_9_CZT_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PA_11_SER1_POW_CTL" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PA_12_SER2_POW_CTL" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="12"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PB" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="1"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PB_1_CCU_LIN1_SLP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_2_CCU_LIN2_SLP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_4_CCU_CANFD2_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="4"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_5_CCU_CANFD4_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_6_CHG_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="6"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_7_CCU_CANFD3_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_8_CZF_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_11_CZL_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_13_PUB_CANFD1_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PB_14_PUB_CANFD2_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="14"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PC" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="2"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PC_13" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PD" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="3"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PD_9_PHY_0V9_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PD_11" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PE" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="4"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PE_0_BRAKE_PEDAL_SW_NO_MCU_1V8" 
                             type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_2_SWT_WAKE_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_3_SWT_0V9_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="4"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_5_TBX_PHY_WAKE_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_7_SECU_3V3_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_8_SECU_RST_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_9_SWT_RST_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_10_TBX_PHY_RST_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="10"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_11" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_12" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="12"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_13_SWT_DIS_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_14_BAT2_ADC_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="14"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PE_15" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="15"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PF" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="5"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PF_0" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PF_1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PF_2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PF_3_IG1_MCU_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PF_4_OBD_ACT_MCU_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="4"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PF_15" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="15"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PG" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="6"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PG_1_NORFLASH_RST_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PG_2_BMC_PWM_DIG_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PG_3_PHY_1V8_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PG_5_CAN_5V0_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PH" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="7"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PH_0_VDD_EFUSE_POW__CTL_1V8" 
                             type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_5" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_6" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="6"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_7_BRAKE_PEDAL_SW_NC_MCU_1V8" 
                             type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_8_PHY_3V3_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_9_SWT_3V3_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PH_10_BAT1_ADC_CTL_1V8" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="10"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PJ" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="9"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PJ_0_SRS_PWM_POW_CTL_1V8" 
                             type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PJ_3_CZR_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PK" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="10"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PK_11_IDC_CANFD_STP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="DioPort_PL" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="11"/>
                    <d:var name="DioPortUseScmi" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="DioPortAccessType" type="ENUMERATION" 
                           value="DIO_READ_WRITE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PL_1_IDC_CANFD_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PL_2_OBD_CAN_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PL_4_TBOX_CAN_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="4"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PL_5_CCU_CANFD1_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                      <d:ctr name="PL_14" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="14"/>
                        <d:lst name="DioChannelEcucPartitionRef"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                    <d:lst name="DioPortEcucPartitionRef"/>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="DioGeneral" type="IDENTIFIABLE">
                <d:var name="DioDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SIUL2DioIPDevErrorDetect" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioReversePortBits" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioFlipChannelApi" type="BOOLEAN" value="true"/>
                <d:var name="DioScmiWriteChannelGroupApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioReadZeroForUndefinedPortPins" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioMaskedWritePortApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioScmiMaskedReadPortApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioScmiPlatformSupport" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="DioEcucPartitionRef"/>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="120">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
