<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Gpt" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Gpt" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Gpt"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="GptChannelConfigSet" type="IDENTIFIABLE">
                <d:lst name="GptChannelConfiguration" type="MAP">
                  <d:ctr name="GptChannelConfiguration_0" type="IDENTIFIABLE">
                    <d:var name="GptChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:lst name="GptChannelEcucPartitionRef"/>
                    <d:var name="GptHwIp" type="ENUMERATION" value="PIT"/>
                    <d:ref name="GptModuleRef" type="REFERENCE" 
                           value="ASPath:/Gpt/Gpt/GptChannelConfigSet/GptPit_0/GptPitChannels_0"/>
                    <d:var name="GptChannelMode" type="ENUMERATION" 
                           value="GPT_CH_MODE_ONESHOT">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="GptChannelTickFrequency" type="FLOAT" 
                           value="4.0E7"/>
                    <d:ref name="GptChannelClkSrcRef" type="REFERENCE" 
                           value="ASPath:/Gpt/Gpt/GptDriverConfiguration/GptClockReferencePoint_1"/>
                    <d:var name="GptChannelTickValueMax" type="INTEGER" 
                           value="4294967295">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="GptEnableWakeup" type="BOOLEAN" value="false"/>
                    <d:var name="GptNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ctr name="GptWakeupConfiguration" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="GptWakeupSourceRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="GptPit" type="MAP">
                  <d:ctr name="GptPit_0" type="IDENTIFIABLE">
                    <d:var name="GptPitModule" type="ENUMERATION" value="PIT_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PitFreezeEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="GptPitChannels" type="MAP">
                      <d:ctr name="GptPitChannels_0" type="IDENTIFIABLE">
                        <d:var name="GptPitChannel" type="ENUMERATION" 
                               value="CH_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="ChainMode" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:lst name="GptStm" type="MAP"/>
                <d:lst name="GptRtc" type="MAP"/>
                <d:lst name="GptFtm" type="MAP"/>
              </d:ctr>
              <d:lst name="GptHwConfiguration" type="MAP">
                <d:ctr name="GptHwConfiguration_0" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_0_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_1" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_0_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_2" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_0_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_3" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_0_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_4" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_1_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_5" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_1_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_6" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_1_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_7" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_1_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_8" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_2_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_9" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_2_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_10" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_2_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_11" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_2_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_12" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_3_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_13" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_3_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_14" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_3_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_15" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_3_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_16" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_4_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_17" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_4_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_18" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_4_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_19" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_4_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_20" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_5_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_21" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_5_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_22" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_5_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_23" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_5_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_24" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_6_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_25" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_6_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_26" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_6_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_27" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_6_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_28" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_7_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_29" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_7_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_30" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_7_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_31" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_7_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_32" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_8_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_33" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_8_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_34" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_8_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_35" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_8_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_36" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_9_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_37" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_9_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_38" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_9_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_39" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="STM_9_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_40" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_10_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_41" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_10_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_42" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_10_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_43" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_10_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_44" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_11_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_45" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_11_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_46" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_11_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_47" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_11_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_48" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_12_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_49" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_12_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_50" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_12_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_51" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_12_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_52" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_53" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_54" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_55" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_56" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_4">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_57" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_5">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_58" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_6">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_59" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_60" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_61" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_62" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_63" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_4">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_64" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_1_CH_5">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_65" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_66" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_67" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_68" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_69" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_4">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_70" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_0_CH_5">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_71" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_72" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_73" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_74" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_3">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_75" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_4">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_76" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="FTM_1_CH_5">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_77" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="RTC_0_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_78" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_0_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_79" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_1_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_80" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_2_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_81" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_3_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_82" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_4_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_83" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_5_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_84" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_6_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_85" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_7_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_86" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_8_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_87" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_9_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_88" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_10_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_89" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_11_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_90" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="STM_12_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_91" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="FTM_0_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptHwConfiguration_92" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" 
                         value="FTM_1_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:lst>
              <d:ctr name="GptConfigurationOfOptApiServices" 
                     type="IDENTIFIABLE">
                <d:var name="GptDeinitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptEnableDisableNotificationApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptTimeElapsedApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptTimeRemainingApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptWakeupFunctionalityApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptPredefTimerFunctionalityApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="GptAutosarExt" type="IDENTIFIABLE">
                <d:var name="GptEnableDualClockMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptChangeNextTimeoutValueApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ChainModeApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptStandbyWakeupSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="GptDriverConfiguration" type="IDENTIFIABLE">
                <d:var name="GptDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptPredefTimer100us32bitEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptPredefTimer1usEnablingGrade" type="ENUMERATION" 
                       value="GPT_PREDEF_TIMER_1US_16_24_32BIT_ENABLED"/>
                <d:var name="GptTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptTimeoutDuration" type="INTEGER" value="800">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptReportWakeupSource" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="GptEcucPartitionRef"/>
                <d:ref name="GptKernelEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:lst name="GptClockReferencePoint" type="MAP">
                  <d:ctr name="GptClockReferencePoint_0" type="IDENTIFIABLE">
                    <d:ref name="GptClockReference" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/I2C_CLK"/>
                  </d:ctr>
                  <d:ctr name="GptClockReferencePoint_1" type="IDENTIFIABLE">
                    <d:ref name="GptClockReference" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="GptPredefTimerConfiguration" type="IDENTIFIABLE">
                <d:ctr name="GptPredefTimer_1us_16Bit" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="true"/>
                  <d:var name="GptHwChannel" type="ENUMERATION" 
                         value="STM_0_PREDEF">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@CALC</a:v>
                      <a:v>@DEF</a:v>
                    </a:a>
                  </d:var>
                  <d:ref name="GptChannelClkSrcRef" type="REFERENCE" 
                         value="ASPath:/Gpt/Gpt/GptDriverConfiguration/GptClockReferencePoint_1"/>
                  <d:var name="GptFtmChannelClkSrc" type="ENUMERATION" 
                         value="FTM_GPT_IP_CLOCK_SOURCE_NONE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="GptChannelPrescaler" type="INTEGER" value="40">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@CALC</a:v>
                      <a:v>@DEF</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptFreezeEnable" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptPredefTimer_1us_24Bit" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="true"/>
                  <d:var name="GptHwChannel" type="ENUMERATION" 
                         value="STM_1_PREDEF"/>
                  <d:ref name="GptChannelClkSrcRef" type="REFERENCE" 
                         value="ASPath:/Gpt/Gpt/GptDriverConfiguration/GptClockReferencePoint_1"/>
                  <d:var name="GptChannelPrescaler" type="INTEGER" value="40">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@CALC</a:v>
                      <a:v>@DEF</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptFreezeEnable" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptPredefTimer_1us_32Bit" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="true"/>
                  <d:var name="GptHwChannel" type="ENUMERATION" 
                         value="STM_2_PREDEF"/>
                  <d:ref name="GptChannelClkSrcRef" type="REFERENCE" 
                         value="ASPath:/Gpt/Gpt/GptDriverConfiguration/GptClockReferencePoint_1"/>
                  <d:var name="GptChannelPrescaler" type="INTEGER" value="40">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@CALC</a:v>
                      <a:v>@DEF</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptFreezeEnable" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="GptPredefTimer_100us_32Bit" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="GptHwChannel" type="ENUMERATION" value="">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:ref name="GptChannelClkSrcRef" type="REFERENCE" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:var name="GptChannelPrescaler" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptFreezeEnable" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="100">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
