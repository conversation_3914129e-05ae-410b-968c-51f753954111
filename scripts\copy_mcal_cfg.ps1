#!/usr/bin/env pwsh

# Script to copy YL2_Config_G3 into build dir

$SOURCE_DIR = "../YL2_Config_G3"
$BASE_DIR = "SRC/HSW/MCAL_Cfg"

# Check if the base directory exists
if (-not (Test-Path $BASE_DIR -PathType Container)) {
    Write-Host "Directory '$BASE_DIR' does not exist, try to mkdir."
    New-Item -ItemType Directory -Path $BASE_DIR -Force | Out-Null
}

Write-Host "Copying $SOURCE_DIR recursively to SRC/HSW/ ..."

# Copy all files and dirs from SOURCE_DIR to BASE_DIR
if (Test-Path $SOURCE_DIR) {
    Copy-Item -Path "$SOURCE_DIR\*" -Destination $BASE_DIR -Recurse -Force
} else {
    Write-Warning "Source directory '$SOURCE_DIR' does not exist!"
    exit 1
}

Write-Host "Done."
