<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr name="ImporterExporterAdditions">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ImporterExporterAdditions"/>
    <d:var name="Version_AutosarImporter" value="2">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="Version_SystemDescriptionImporter" value="4">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="Version_SystemDescriptionExporter" value="1">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
  </d:ctr>
  <d:ctr name="General">
    <a:a name="DEF" value="XPath:/PreferencesSchema/General"/>
    <d:var name="Version" value="29.0.0"/>
    <d:var name="ReleaseVersion" value="4.3.1"/>
    <d:var name="ModelExtenderCompatibility" value="TRUE"/>
    <d:lst name="ModelExtender" type="MAP">
      <d:ctr name="ModelEcuConfiguration"/>
      <d:ctr name="SystemModel2"/>
    </d:lst>
    <d:var name="System" value=""/>
    <d:var name="EcuInstance" value=""/>
    <d:lst name="ModuleConfigurations" type="MAP">
      <d:ctr name="Adc">
        <d:var name="ConfigurationFileURL" value="config\Adc.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Adc_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="BaseNXP">
        <d:var name="ModuleId" value="BaseNXP_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\BaseNXP.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="CanIf">
        <d:var name="ModuleId" value="CanIf_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\CanIf.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Can_43_LLCE">
        <d:var name="ConfigurationFileURL" value="config\Can_43_LLCE.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Can_43_LLCE_TS_T40D11M10I10R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="1.0.10 D2505"/>
      </d:ctr>
      <d:ctr name="Dem">
        <d:var name="ModuleId" value="Dem_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Dem.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Dio">
        <d:var name="ConfigurationFileURL" value="config\Dio.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Dio_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="EcuC">
        <d:var name="ModuleId" value="EcuC_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\EcuC.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="EcuM">
        <d:var name="ModuleId" value="EcuM_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\EcuM.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Fls">
        <d:var name="ModuleId" value="Fls_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Fls.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Gpt">
        <d:var name="ConfigurationFileURL" value="config\Gpt.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Gpt_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="I2c">
        <d:var name="ConfigurationFileURL" value="config\I2c.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="I2c_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="FALSE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="Icu">
        <d:var name="ConfigurationFileURL" value="config\Icu.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Icu_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="Lin_43_LLCE">
        <d:var name="ConfigurationFileURL" value="config\Lin_43_LLCE.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Lin_43_LLCE_TS_T40D11M10I10R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.10 D2505"/>
      </d:ctr>
      <d:ctr name="Llce_Af">
        <d:var name="ConfigurationFileURL" value="config\Llce_Af.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Llce_Af_TS_T40D11M10I10R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="1.0.10 D2505"/>
      </d:ctr>
      <d:ctr name="Mcu">
        <d:var name="ModuleId" value="Mcu_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Mcu.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Os">
        <d:var name="ModuleId" value="Os_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Os.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Platform">
        <d:var name="ModuleId" value="Platform_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Platform.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Pmic">
        <d:var name="ConfigurationFileURL" value="config\Pmic.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Pmic_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="FALSE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="Port">
        <d:var name="ModuleId" value="Port_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Port.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Pwm">
        <d:var name="ConfigurationFileURL" value="config\Pwm.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Pwm_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
      </d:ctr>
      <d:ctr name="Resource">
        <d:var name="ModuleId" value="Resource_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Resource.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Rm">
        <d:var name="ModuleId" value="Rm_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Rm.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Uart">
        <d:var name="ModuleId" value="Uart_TS_T40D11M50I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
        <d:var name="ConfigurationFileURL" value="config\Uart.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
    </d:lst>
    <d:lst name="AutoconfigureTriggers" type="MAP">
      <d:ctr name="AutoHandleId">
        <d:var name="Autoconfigure" value="TRUE"/>
      </d:ctr>
    </d:lst>
    <d:lst name="WizardConfigurations" type="MAP"/>
  </d:ctr>
  <d:ctr name="ECUCNature">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ECUCNature"/>
    <d:var name="ReleaseVersion" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="ECUId" value="CCU"/>
    <d:var name="ConfigurationPath" value="config"/>
    <d:var name="GenerationPath" value="output\generated"/>
    <d:var name="UnixLF" value="FALSE"/>
    <d:var name="UnixLFConfigData" value="FALSE"/>
    <d:var name="DisableMinListChildCreation" value="TRUE"/>
    <d:var name="ProjectSpecificSettings" value="FALSE"/>
    <d:var name="ProjectSpecificSettingsConfigurationProject" value="FALSE"/>
    <d:var name="Target" value="CORTEXM"/>
    <d:var name="Derivate" value="S32G3XXM7"/>
    <d:var name="DefaultPreConfiguration" value="&lt;Use Module Default&gt;"/>
    <d:var name="DefaultRecConfiguration" value="&lt;Use Module Default&gt;"/>
    <d:ctr name="ConfigTime">
      <a:a name="ENABLE" value="false"/>
      <d:var name="PublishedInformation" value="FALSE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PreCompile" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="Link" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PostBuild" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
    </d:ctr>
  </d:ctr>

</datamodel>
