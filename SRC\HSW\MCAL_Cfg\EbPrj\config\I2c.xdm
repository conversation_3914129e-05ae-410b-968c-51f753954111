<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="I2c" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="I2c" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/I2c"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="GeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="I2cDevErrorDetect" type="BOOLEAN" value="false"/>
                <d:var name="I2cTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cDmaUsed" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cDmaTransferErrorDetect" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cDisableDemReportErrorStatus" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cTimeoutDuration" type="INTEGER" value="1000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2c_Callback" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cCallback" type="FUNCTION-NAME" 
                       value="I2c_Callback"/>
                <d:var name="I2c_ErrorCallback" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cErrorCallback" type="FUNCTION-NAME" 
                       value="I2c_ErrorCallback">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="I2cBusReleaseCallback" type="FUNCTION-NAME" 
                       value="I2c_Notification">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="I2cGlobalConfig" type="IDENTIFIABLE">
                <d:lst name="I2cEcucPartitionRef"/>
                <d:lst name="I2cChannel" type="MAP">
                  <d:ctr name="I2cChannel_0" type="IDENTIFIABLE">
                    <d:var name="I2cChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="I2cHwChannel" type="ENUMERATION" value="IIC_0"/>
                    <d:var name="I2cMasterSlaveConfiguration" 
                           type="ENUMERATION" value="MASTER_MODE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="I2cChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="I2cMasterConfiguration" type="IDENTIFIABLE">
                      <d:ref name="I2cClockRef" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/I2C_CLK">
                        <a:a name="ENABLE" value="true"/>
                      </d:ref>
                      <d:var name="I2cAsyncMethod" type="ENUMERATION" 
                             value="I2C_USING_INTERRUPTS">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cMasterGlitchFilter" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2c_BusRecovery" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="I2cTxDmaChannel" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:ref name="I2cRxDmaChannel" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="I2cIbcValue" type="INTEGER" value="36"/>
                      <d:var name="I2cSCLDivider" type="INTEGER" value="288">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSDAHoldDelay" type="INTEGER" value="49">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSCLHoldStart" type="INTEGER" value="142">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSCLHoldStop" type="INTEGER" value="145">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cBaudRate" type="FLOAT" 
                             value="231481.48148148146">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="I2cSlaveConfiguration" type="IDENTIFIABLE">
                      <d:var name="I2cSlaveAddress" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cSlaveListening" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cSlaveGlitchFilter" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="I2cChannel_1" type="IDENTIFIABLE">
                    <d:var name="I2cChannelId" type="INTEGER" value="1"/>
                    <d:var name="I2cHwChannel" type="ENUMERATION" value="IIC_4"/>
                    <d:var name="I2cMasterSlaveConfiguration" 
                           type="ENUMERATION" value="MASTER_MODE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="I2cChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="I2cMasterConfiguration" type="IDENTIFIABLE">
                      <d:ref name="I2cClockRef" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/I2C_CLK">
                        <a:a name="ENABLE" value="true"/>
                      </d:ref>
                      <d:var name="I2cAsyncMethod" type="ENUMERATION" 
                             value="I2C_USING_INTERRUPTS">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cMasterGlitchFilter" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2c_BusRecovery" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="I2cTxDmaChannel" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:ref name="I2cRxDmaChannel" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="I2cIbcValue" type="INTEGER" value="36"/>
                      <d:var name="I2cSCLDivider" type="INTEGER" value="288">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSDAHoldDelay" type="INTEGER" value="49">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSCLHoldStart" type="INTEGER" value="142">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cSCLHoldStop" type="INTEGER" value="145">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="I2cBaudRate" type="FLOAT" 
                             value="231481.48148148146">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="I2cSlaveConfiguration" type="IDENTIFIABLE">
                      <d:var name="I2cSlaveAddress" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cSlaveListening" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="I2cSlaveGlitchFilter" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:ctr name="I2cDemEventParameterRefs" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:ref name="I2C_E_TIMEOUT_FAILURE" type="REFERENCE" 
                         value="ASPath:/Dem/Dem/DemConfigSet_0/I2C_E_TIMEOUT_FAILURE">
                    <a:a name="ENABLE" value="TRUE"/>
                    <a:a name="IMPORTER_INFO" value="@CALC(SvcAs,dem.events,1)"/>
                  </d:ref>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
