<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Icu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Icu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Icu"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="IcuConfigSet" type="IDENTIFIABLE">
                <d:var name="IcuMaxChannel" type="INTEGER" value="2"/>
                <d:lst name="IcuChannel" type="MAP">
                  <d:ctr name="IcuChannel_1_0" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:lst name="IcuChannelEcucPartitionRef"/>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuFtm_1/IcuFtmChannels_0"/>
                    <d:var name="IcuDMAChannelEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuOverflowNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_1_2" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:lst name="IcuChannelEcucPartitionRef"/>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuFtm_1/IcuFtmChannels_2"/>
                    <d:var name="IcuDMAChannelEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuOverflowNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="IcuFtm" type="MAP">
                  <d:ctr name="IcuFtm_1" type="IDENTIFIABLE">
                    <d:var name="IcuFtmModule" type="INTEGER" value="1"/>
                    <d:var name="IcuFtmClockSource" type="ENUMERATION" 
                           value="SYSTEM_CLOCK">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuFtmPrescaler" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuFtmPrescalerAlternate" type="INTEGER" 
                           value="128">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuFtmDebugMode" type="ENUMERATION" 
                           value="MODE_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuFtmModValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="IcuFtmChannels" type="MAP">
                      <d:ctr name="IcuFtmChannels_0" type="IDENTIFIABLE">
                        <d:var name="IcuFtmChannel" type="INTEGER" value="0"/>
                        <d:var name="IcuFtmFilter" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="IcuFtmChannels_2" type="IDENTIFIABLE">
                        <d:var name="IcuFtmChannel" type="INTEGER" value="2"/>
                        <d:var name="IcuFtmFilter" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:lst name="IcuSiul2" type="MAP"/>
                <d:lst name="IcuWkpu" type="MAP"/>
                <d:lst name="IcuHwInterruptConfigList" type="MAP">
                  <d:ctr name="IcuHwInterruptConfigList_0" type="IDENTIFIABLE">
                    <d:var name="IcuIsrHwId" type="ENUMERATION" 
                           value="FTM_1_CH_0"/>
                  </d:ctr>
                  <d:ctr name="IcuHwInterruptConfigList_1" type="IDENTIFIABLE">
                    <d:var name="IcuIsrHwId" type="ENUMERATION" 
                           value="FTM_1_CH_2"/>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="IcuGeneral" type="IDENTIFIABLE">
                <d:var name="IcuDevErrorDetect" type="BOOLEAN" value="false"/>
                <d:var name="WkpuPullSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuReportWakeupSource" type="BOOLEAN" value="false"/>
                <d:var name="IcuEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="IcuEcucPartitionRef"/>
                <d:var name="IcuScmiPlatformSupport" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="IcuKernelEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="IcuAutosarExt" type="IDENTIFIABLE">
                <d:var name="IcuEnableDualClockMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuOverflowNotificationApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetInputLevelApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetCaptureRegisterValueApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuWkpuStandbyWakeupSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="IcuOptionalApis" type="IDENTIFIABLE">
                <d:var name="IcuDeInitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuDisableWakeupApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEdgeCountApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEnableWakeupApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetDutyCycleValuesApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetInputStateApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetTimeElapsedApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSetModeApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSignalMeasurementApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuTimestampApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuWakeupFunctionalityApi" type="BOOLEAN" 
                       value="false"/>
                <d:var name="IcuEdgeDetectApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="IcuScmiApiConfiguration" type="IDENTIFIABLE">
                <d:var name="IcuGetChannelIdFromExtIrqApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEnableDisableInterruptsApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="122">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
