/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef SHARED_SETTINGS_IP_VS_Headless_PBCFG_H
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_H

/**
*   @file       SharedSettings_Ip_VS_Headless_PBcfg.h
*   @version    5.0.0
*
*   @brief   AUTOSAR Mcu - Post-Build(PB) configuration file code template.
*   @details Code template for Post-Build(PB) configuration file generation.
*
*   @addtogroup SHARED_SETTINGS_DRIVER_CONFIGURATION SharedSettings Ip Driver
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "SharedSettings_Ip_Types.h"


/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_VENDOR_ID                      43
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MAJOR_VERSION       4
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MINOR_VERSION       4
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_REVISION_VERSION    0
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MAJOR_VERSION               5
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MINOR_VERSION               0
#define SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_PATCH_VERSION               0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and SharedSettings_Ip_Types.h file are of the same vendor */
#if (SHARED_SETTINGS_IP_VS_Headless_PBCFG_VENDOR_ID != SHARED_SETTINGS_IP_TYPES_VENDOR_ID)
    #error "SharedSettings_Ip_VS_Headless_PBcfg.h and SharedSettings_Ip_Types.h have different vendor ids"
#endif

/* Check if header file and SharedSettings_Ip_Types.h file are of the same Autosar version */
#if ((SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MAJOR_VERSION != SHARED_SETTINGS_IP_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MINOR_VERSION != SHARED_SETTINGS_IP_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_REVISION_VERSION != SHARED_SETTINGS_IP_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of SharedSettings_Ip_VS_Headless_PBcfg.h and SharedSettings_Ip_Types.h are different"
#endif

/* Check if header file and SharedSettings_Ip_Types.h file are of the same Software version */
#if ((SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MAJOR_VERSION != SHARED_SETTINGS_IP_TYPES_SW_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MINOR_VERSION != SHARED_SETTINGS_IP_TYPES_SW_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_PATCH_VERSION != SHARED_SETTINGS_IP_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of SharedSettings_Ip_VS_Headless_PBcfg.h and SharedSettings_Ip_Types.h are different"
#endif



/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */
#endif /* SHARED_SETTINGS_IP_VS_Headless_PBCFG_H */


