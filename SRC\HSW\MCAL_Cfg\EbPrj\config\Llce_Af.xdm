<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Llce_Af" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Llce_Af" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M10I10R0/Llce_Af"/>
              <d:ctr name="LlceAfGeneral" type="IDENTIFIABLE">
                <d:var name="EnableAdvancedFeatureMode" type="BOOLEAN" 
                       value="false"/>
                <d:lst name="CanAdvancedFeature" type="MAP">
                  <d:ctr name="CanAdvancedFeature_PassThrough" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false"/>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_PassThrough"/>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" 
                             value=""/>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_CanIdRemap" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_CanIdRemap"/>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_Can2CanFd" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_Can2CanFd"/>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_CanFd2Can" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_CanFd2Can"/>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_Can2CanFd_CanIdRemap" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_Can2CanFd_CanIdRemap"/>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_Can2Eth_AVTP_NTSCF_BRIEF" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false"/>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2EthRoutingTable_AVTP_NTSCF_BRIEF"/>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanAdvancedFeature_Can2Eth_UDP" 
                         type="IDENTIFIABLE">
                    <d:var name="LoggingFeatureEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="HostReceiveEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AuthenticationFrameEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CustomProcessingIndex" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="Can2CanRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2CanRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="Can2EthRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2EthRoutingTableRef" type="REFERENCE" 
                             value="ASPath:/Llce_Af/Llce_Af/LlceAfGeneral/Can2EthRoutingTable_UDP"/>
                    </d:ctr>
                    <d:ctr name="Can2OtherRoutingEnable" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="Can2OtherRoutingTableRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="Can2CanRoutingTable" type="MAP">
                  <d:ctr name="Can2CanRoutingTable_PassThrough" 
                         type="IDENTIFIABLE">
                    <d:var name="CanFd2Can" type="BOOLEAN" value="false"/>
                    <d:var name="Can2CanFd" type="BOOLEAN" value="false"/>
                    <d:var name="CanIdRemapping" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:var name="ForceExtendedID" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanDestinationList" type="MAP">
                      <d:ctr name="CanDestinationList_0" type="IDENTIFIABLE">
                        <d:ref name="Can2CanControllerRef" type="REFERENCE" 
                               value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="Can2CanRoutingTable_Can2CanFd" 
                         type="IDENTIFIABLE">
                    <d:var name="CanFd2Can" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="Can2CanFd" type="BOOLEAN" value="true"/>
                    <d:var name="CanIdRemapping" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="ForceExtendedID" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanDestinationList" type="MAP">
                      <d:ctr name="CanDestinationList_0" type="IDENTIFIABLE">
                        <d:ref name="Can2CanControllerRef" type="REFERENCE" 
                               value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="Can2CanRoutingTable_CanFd2Can" 
                         type="IDENTIFIABLE">
                    <d:var name="CanFd2Can" type="BOOLEAN" value="true"/>
                    <d:var name="Can2CanFd" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdRemapping" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:var name="ForceExtendedID" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanDestinationList" type="MAP">
                      <d:ctr name="CanDestinationList_0" type="IDENTIFIABLE">
                        <d:ref name="Can2CanControllerRef" type="REFERENCE" 
                               value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="Can2CanRoutingTable_Can2CanFd_CanIdRemap" 
                         type="IDENTIFIABLE">
                    <d:var name="CanFd2Can" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="Can2CanFd" type="BOOLEAN" value="true"/>
                    <d:var name="CanIdRemapping" type="INTEGER" value="999">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="ForceExtendedID" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanDestinationList" type="MAP">
                      <d:ctr name="CanDestinationList_0" type="IDENTIFIABLE">
                        <d:ref name="Can2CanControllerRef" type="REFERENCE" 
                               value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="Can2CanRoutingTable_CanIdRemap" 
                         type="IDENTIFIABLE">
                    <d:var name="CanFd2Can" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="Can2CanFd" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdRemapping" type="INTEGER" value="16">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="ForceExtendedID" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanDestinationList" type="MAP">
                      <d:ctr name="CanDestinationList_0" type="IDENTIFIABLE">
                        <d:ref name="Can2CanControllerRef" type="REFERENCE" 
                               value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:var name="CAN2ETHWithExternalRingBuffer" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="Can2EthRoutingTable" type="MAP"/>
                <d:lst name="Can2OtherRoutingTable" type="MAP"/>
                <d:ctr name="Eth2Can" type="IDENTIFIABLE">
                  <d:var name="Eth2CanEnable" type="BOOLEAN" value="false"/>
                  <d:var name="Eth2CanBufferCount" type="INTEGER" value="72">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="Eth2CanBufferSize" type="INTEGER" value="1000">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:lst name="Eth2CanEnabledFormats"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="99">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="10"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
