<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="CanIf" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="CanIf" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/CanIf"/>
              <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="CanIfCtrlDrvCfg" type="MAP">
                <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                <d:ctr name="CanIfCtrlDrvCfg" type="IDENTIFIABLE">
                  <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                  <d:ref name="CanIfCtrlDrvNameRef" type="REFERENCE" 
                         value="ASPath:/Can_43_LLCE/Can/CanGeneral">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                  </d:ref>
                  <d:lst name="CanIfCtrlCfg" type="MAP">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:ctr name="CAN0_LlceCan0" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN0_LlceCan0">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN1_LlceCan1" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN2_LlceCan2" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN2_LlceCan2">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN3_LlceCan3" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="3">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN3_LlceCan3">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN4_LlceCan4" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="4">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN4_LlceCan4">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN5_LlceCan5" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="5">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN5_LlceCan5">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN8_LlceCan8" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="6">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN8_LlceCan8">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN9_LlceCan9" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="7">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN9_LlceCan9">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN10_LlceCan10" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="8">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN10_LlceCan10">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN11_LlceCan11" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="9">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN11_LlceCan11">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN12_LlceCan12" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="10">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN12_LlceCan12">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN13_LlceCan13" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="11">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN13_LlceCan13">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN14_LlceCan14" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="12">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN14_LlceCan14">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                    <d:ctr name="CAN15_LlceCan15" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:var name="CanIfCtrlId" type="INTEGER" value="13">
                        <a:a name="IMPORTER_INFO" value="@CALC"/>
                      </d:var>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN15_LlceCan15">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      </d:ref>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
