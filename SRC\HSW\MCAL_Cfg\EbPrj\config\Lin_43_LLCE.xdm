<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Lin_43_LLCE" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Lin" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/Lin_43_LLCE_TS_T40D11M10I10R0/Lin"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="AutosarExt" type="IDENTIFIABLE">
                <d:var name="LinDisableDemReportErrorStatus" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="LinGeneral" type="IDENTIFIABLE">
                <d:var name="LinDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinTimeoutDuration" type="INTEGER" value="65000"/>
                <d:var name="LinVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="LinEcucPartitionRef"/>
              </d:ctr>
              <d:ctr name="LinDemEventParameterRefs" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="false"/>
                <d:ref name="LIN_E_TIMEOUT" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="LinGlobalConfig" type="IDENTIFIABLE">
                <d:lst name="LinChannel" type="MAP">
                  <d:ctr name="LinChannel_0" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="LinNodeType" type="ENUMERATION" value="MASTER">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" value="9600">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_14">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_21">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LinHWCh_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelDisableFrameTimeout" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="LinChannel_1" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="LinNodeType" type="ENUMERATION" value="SLAVE"/>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" value="9600">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_14">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_13">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LinHWCh_1"/>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelDisableFrameTimeout" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="LinChannel_2" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="LinNodeType" type="ENUMERATION" value="MASTER">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" value="9600">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_14">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_13">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LinHWCh_2"/>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelDisableFrameTimeout" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="LinChannel_3" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="3">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="LinNodeType" type="ENUMERATION" value="MASTER">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" value="9600">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_14">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthMaster" type="ENUMERATION" 
                           value="BL_13">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="BreakLengthSlave" type="ENUMERATION" 
                           value="BL_10">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LinHWCh_3"/>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/LIN_PE_CLK">
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelDisableFrameTimeout" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="0"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                      <a:a name="VARIANTS" type="Variant">
                        <variant:pbvcond>
                          <variant:criterion 
                                             value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                          <variant:cond>
                            <variant:tst expr="1"/>
                          </variant:cond>
                        </variant:pbvcond>
                      </a:a>
                    </d:ref>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="82">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="10">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="LLCE">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
