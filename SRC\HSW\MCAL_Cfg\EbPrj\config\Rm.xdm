<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Rm" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Rm" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Rm"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="RmGeneral" type="IDENTIFIABLE">
                <d:var name="Rm_VersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="RmDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="RmEnableUserModeSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="RmEnableMultiCore" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="RmEnableXRDCSupport" type="BOOLEAN" value="false"/>
                <d:var name="RmEnableSema42Support" type="BOOLEAN" value="false"/>
                <d:var name="RmMpuM7Configurable" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="RmEnableMscmSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="RmConfigSet" type="IDENTIFIABLE">
                <d:ctr name="Xrdc_Configuration" type="IDENTIFIABLE">
                  <d:var name="XrdcDevErrorDetect" type="BOOLEAN" value="false"/>
                  <d:var name="XrdcRegistersLock" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="XrdcCRLockBit" type="BOOLEAN" value="false"/>
                  <d:var name="XrdcPIDRegisterLock" type="ENUMERATION" 
                         value="XRDC_PID_UNLOCKED">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:lst name="Rm_XRDC_Domain_Assignment" type="MAP"/>
                <d:lst name="Rm_XRDC_Memory_Config" type="MAP"/>
                <d:lst name="Rm_XRDC_Peripheral_Config" type="MAP"/>
                <d:ctr name="MPU_M7_Configuration" type="IDENTIFIABLE">
                  <d:var name="MpuDevErrorDetect" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:lst name="MPU_M7_ModuleConfig" type="MAP">
                    <d:ctr name="MPU_M7_ModuleConfig_0" type="IDENTIFIABLE">
                      <d:var name="DefaultMapEnable" type="BOOLEAN" value="true"/>
                      <d:var name="RunInHFNMIEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="MemManageInterruptEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="RegionConfig" type="MAP">
                        <d:ctr name="RegionConfig_0" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="4294967295">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="4294967296">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_STRONG_ORDER">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_UNPRIV_NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true"/>
                        </d:ctr>
                        <d:ctr name="RegionConfig_1" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="536870911">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="536870912">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE"/>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE"/>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_2" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="536870912">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="538968063">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="2097152">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_STRONG_ORDER">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_3" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="3">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="583008256">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="583024639">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" value="16384">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE"/>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_4" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="4">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="603979776">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="604012543">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" value="32768">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_5" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="5">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="872415232"/>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="876609535"/>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="4194304"/>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_6" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="6">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="876609536"/>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="878706687"/>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="2097152"/>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE"/>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE"/>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE"/>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_7" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="7">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="877658112"/>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="878706687"/>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="1048576"/>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_NORMAL_CACHEABLE"/>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RWX_UNPRIV_RWX">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_8" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="8">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="1073741824"/>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="1610612735"/>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="536870912"/>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_STRONG_ORDER"/>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RW_UNPRIV_RW"/>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="RegionConfig_9" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="9">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="1124073472"/>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="1140850687"/>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="16777216"/>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_STRONG_ORDER">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RW_UNPRIV_RW"/>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true"/>
                        </d:ctr>
                        <d:ctr name="RegionConfig_10" type="IDENTIFIABLE">
                          <d:var name="RegionNumber" type="INTEGER" value="10">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="StartAddress" type="INTEGER" 
                                 value="3758096384">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="EndAddress" type="INTEGER" 
                                 value="3759144959">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="RegionSize" type="INTEGER" 
                                 value="1048576">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="MemoryType" type="ENUMERATION" 
                                 value="MPU_M7_MEM_STRONG_ORDER">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="AccessRights" type="ENUMERATION" 
                                 value="MPU_M7_PRIV_RW_UNPRIV_RW"/>
                          <d:var name="OuterCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="InnerCachePolicy" type="ENUMERATION" 
                                 value="MPU_M7_CACHE_POLICY_NO_CACHE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="SubregionMask" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="Shareable" type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:var name="PhysicalCoreID" type="ENUMERATION" 
                             value="CORTEX_M7_CORE0"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
                <d:ctr name="Sema42_ModuleConfig" type="IDENTIFIABLE">
                  <d:var name="Sema42DevErrorDetect" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="Sema42ResetAllGates" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:lst name="Sema42LogicChannelConfiguration" type="MAP"/>
                </d:ctr>
                <d:ctr name="Mscm_Configuration" type="IDENTIFIABLE">
                  <d:var name="MscmDevErrorDetect" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:lst name="MscmConfig" type="MAP"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
