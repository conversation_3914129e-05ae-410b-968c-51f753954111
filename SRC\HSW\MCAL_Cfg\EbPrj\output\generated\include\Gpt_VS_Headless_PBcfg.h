/*==================================================================================================
* Project : RTD AUTOSAR 4.4
* Platform : CORTEXM
* Peripheral : Stm_Pit_Rtc_Ftm
* Dependencies : none
*
* Autosar Version : 4.4.0
* Autosar Revision : ASR_REL_4_4_REV_0000
* Autosar Conf.Variant :
* SW Version : 5.0.0
* Build Version : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
* Copyright 2020-2025 NXP
*
* NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
* used strictly in accordance with the applicable license terms. By expressly
* accepting such terms or by downloading, installing, activating and/or otherwise
* using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms. If you do not agree to be
* bound by the applicable license terms, then you may not retain, install,
* activate or otherwise use the software.
==================================================================================================*/

#ifndef GPT_VS_Headless_PBCFG_H
#define GPT_VS_Headless_PBCFG_H

/**
*   @file           Gpt_PBcfg.h
*
*   @addtogroup     gpt Gpt Driver
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_VS_Headless_PBCFG_H                    43
#define GPT_AR_RELEASE_MAJOR_VERSION_VS_Headless_PBCFG_H     4
#define GPT_AR_RELEASE_MINOR_VERSION_VS_Headless_PBCFG_H     4
#define GPT_AR_RELEASE_REVISION_VERSION_VS_Headless_PBCFG_H  0
#define GPT_SW_MAJOR_VERSION_VS_Headless_PBCFG_H             5
#define GPT_SW_MINOR_VERSION_VS_Headless_PBCFG_H             0
#define GPT_SW_PATCH_VERSION_VS_Headless_PBCFG_H             0
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define GPT_CONFIG_VS_Headless_PB \
        extern const Gpt_ConfigType Gpt_Config_VS_Headless;


#ifdef __cplusplus
}
#endif
/** @} */
#endif  /* GPT_VS_Headless_PBCFG_H */
