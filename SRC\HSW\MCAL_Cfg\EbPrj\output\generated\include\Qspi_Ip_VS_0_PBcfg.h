/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : IPV_QSPI
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef QSPI_IP_VS_0_PBCFG_H
#define QSPI_IP_VS_0_PBCFG_H

/**
 *   @file       Qspi_Ip_VS_0_PBcfg.h
 *
 *   @addtogroup IPV_QSPI QSPI IPV Driver
 *   @implements Qspi_Ip_PBcfg.h_Artifact
 *   @{
 */

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Qspi_Ip_Types.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define QSPI_IP_VS_0_PBCFG_VENDOR_ID                    43

#define QSPI_IP_VS_0_PBCFG_AR_RELEASE_MAJOR_VERSION     4
#define QSPI_IP_VS_0_PBCFG_AR_RELEASE_MINOR_VERSION     4
#define QSPI_IP_VS_0_PBCFG_AR_RELEASE_REVISION_VERSION  0

#define QSPI_IP_VS_0_PBCFG_SW_MAJOR_VERSION             5
#define QSPI_IP_VS_0_PBCFG_SW_MINOR_VERSION             0
#define QSPI_IP_VS_0_PBCFG_SW_PATCH_VERSION             0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Qspi_Ip_Types.h header file are of the same vendor */
#if (QSPI_IP_VS_0_PBCFG_VENDOR_ID != FLS_QSPI_TYPES_VENDOR_ID)
    #error "Qspi_Ip_VS_0_PBcfg.h and Qspi_Ip_Types.h have different vendor ids"
#endif
/* Check if current file and Qspi_Ip_Types.h header file are of the same Autosar version */
#if ((QSPI_IP_VS_0_PBCFG_AR_RELEASE_MAJOR_VERSION    != FLS_QSPI_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (QSPI_IP_VS_0_PBCFG_AR_RELEASE_MINOR_VERSION    != FLS_QSPI_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (QSPI_IP_VS_0_PBCFG_AR_RELEASE_REVISION_VERSION != FLS_QSPI_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
#error "AutoSar Version Numbers of Qspi_Ip_VS_0_PBcfg.h and Qspi_Ip_Types.h are different"
#endif
/* Check if current file and Qspi_Ip_Types.h header file are of the same software version */
#if ((QSPI_IP_VS_0_PBCFG_SW_MAJOR_VERSION != FLS_QSPI_TYPES_SW_MAJOR_VERSION) || \
     (QSPI_IP_VS_0_PBCFG_SW_MINOR_VERSION != FLS_QSPI_TYPES_SW_MINOR_VERSION) || \
     (QSPI_IP_VS_0_PBCFG_SW_PATCH_VERSION != FLS_QSPI_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Qspi_Ip_VS_0_PBcfg.h and Qspi_Ip_Types.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/


/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/* Defines for direct access to the virtual LUT table */
/* Configuration: MemCfg_DOPI */
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_READ_DOPI                               0U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_WRITE_DOPI                              6U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_ERASE_DOPI                             11U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_READSR_DOPI                            15U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_WRITESR_DOPI                           21U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_WRITEENABLE_DOPI                       26U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_RESETENABLE_DOPI                       29U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_RESET_DOPI                             32U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_READID_DOPI                            35U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_RDCR2                                  41U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_WRCR2                                  45U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_WRITEENABLE                            49U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_READSR                                 51U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_INITRESET                              54U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_RUNTIMERESET                           58U
#define MEMCFG_DOPI_VS_0_LUT_SEQUENCE_ERASE_DOPI_BLOCK                       62U

/*==================================================================================================
*                                            ENUMS
==================================================================================================*/


/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


/* Controller connections */
extern const Qspi_Ip_ControllerConfigType FlsConfigSet_VS_0_paQspiUnitCfg[2U];

/* Memory configurations */
extern const Qspi_Ip_MemoryConfigType FlsConfigSet_VS_0_paFlashCfg[1U];

/* Memory-controller connections */
extern const Qspi_Ip_MemoryConnectionType FlsConfigSet_VS_0_paFlashConnectionCfg[1U];


#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/**@}*/

#endif    /* #ifndef QSPI_IP_VS_0_PBCFG_H */

