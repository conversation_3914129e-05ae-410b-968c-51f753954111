$names = (
    "CP_Stub\CanIf_TS_T40D11M50I0R0",
    "CP_Stub\Dem_TS_T40D11M50I0R0",
    "CP_Stub\Det_TS_T40D11M50I0R0",
    "CP_Stub\EcuM_TS_T40D11M50I0R0",
    # "CP_Stub\EthIf_TS_T40D11M50I0R0",
    # "CP_Stub\FrIf_TS_T40D11M50I0R0",
    "CP_Stub\LinIf_TS_T40D11M50I0R0",
    "CP_Stub\MemIf_TS_T40D11M50I0R0",
    # "CP_Stub\PcieIf_TS_T40D11M50I0R0",
    "CP_Stub\Rte_TS_T40D11M50I0R0",
    # "CP_Stub\WdgIf_TS_T40D11M50I0R0",
    "LLCE\Can_43_LLCE_TS_T40D11M10I10R0",
    # "LLCE\Fr_43_LLCE_TS_T40D11M10I10R0",
    "LLCE\Lin_43_LLCE_TS_T40D11M10I10R0",
    # "LLCE\Llce_Af_TS_T40D11M10I10R0",
    "RTD\Adc_TS_T40D11M50I0R0",
    "RTD\BaseNXP_TS_T40D11M50I0R0",
    # "RTD\Can_TS_T40D11M50I0R0",
    # "RTD\Crc_TS_T40D11M50I0R0",
    "RTD\Dio_TS_T40D11M50I0R0",
    # "RTD\EcuC_TS_T40D11M50I0R0",
    # "RTD\Eep_TS_T40D11M50I0R0",
    # "RTD\Eth_TS_T40D11M50I0R0",
    # "RTD\EthSwt_TS_T40D11M50I0R0",
    # "RTD\EthTrcv_TS_T40D11M50I0R0",
    # "RTD\Fee_TS_T40D11M50I0R0",
    "RTD\Fls_TS_T40D11M50I0R0",
    # "RTD\Fr_TS_T40D11M50I0R0",
    "RTD\Gpt_TS_T40D11M50I0R0",
    # "RTD\I2c_TS_T40D11M50I0R0",
    "RTD\Icu_TS_T40D11M50I0R0",
    # "RTD\Lin_TS_T40D11M50I0R0",
    # "RTD\Mcl_TS_T40D11M50I0R0",
    "RTD\Mcu_TS_T40D11M50I0R0",
    # "RTD\Ocotp_TS_T40D11M50I0R0",
    # "RTD\Ocu_TS_T40D11M50I0R0",
    # "RTD\Os_TS_T40D11M50I0R0",
    # "RTD\Pcie_TS_T40D11M50I0R0",
    "RTD\Platform_TS_T40D11M50I0R0",
    # "RTD\Pmic_TS_T40D11M50I0R0",
    "RTD\Port_TS_T40D11M50I0R0",
    "RTD\Pwm_TS_T40D11M50I0R0",
    # "RTD\Qdec_TS_T40D11M50I0R0",
    # "RTD\Resource_TS_T40D11M50I0R0",
    "RTD\Rm_TS_T40D11M50I0R0",
    # "RTD\Serdes_TS_T40D11M50I0R0",
    # "RTD\Spi_TS_T40D11M50I0R0",
    # "RTD\Thermal_TS_T40D11M50I0R0",
    "RTD\Uart_TS_T40D11M50I0R0",
    # "RTD\Wdg_43_VR5510_TS_T40D11M50I0R0",
    # "RTD\Wdg_TS_T40D11M50I0R0"
    ""
)

$SOURCE_DIR = "../MCAL_Static_G3"
$SOURCE_DIR_PATH = (Resolve-Path -Path $SOURCE_DIR).Path
$DEST_DIR = "./SRC/HSW/MCAL_Static"

$dirs = @()

$filters = @("include", "src", "header", "build_files")
foreach ($n in $names) {
    $d = Get-ChildItem -Path "$SOURCE_DIR\$n" -Recurse -Depth 0 -Directory
    # select directories that are in $filters
    $dirs += $d | Where-Object { $filters -contains $_.Name }
}


foreach ($d in $dirs) {
    echo $d.FullName
    $item_relative = $d.FullName.Substring($SOURCE_DIR_PATH.Length)
    $dest_path = Join-Path $DEST_DIR $item_relative
    # echo $dest_path
    Copy-Item -Path $d.FullName -Destination $dest_path -Recurse -Force
}

$maks = @()
foreach ($d in $names) {
    $mak = Get-ChildItem -Path "$SOURCE_DIR\$n" -Filter "*.mak" -Recurse -Depth 1 -File
    $maks += $mak
}

foreach ($d in ("LLCE", "RTD", "CP_Stub")) {
    $mak = Get-ChildItem -Path "$SOURCE_DIR\$n" -Filter "*.c" -Recurse -Depth 1 -File
    $maks += $mak
}

foreach ($m in $maks) {
    $item_relative = $m.FullName.Substring($SOURCE_DIR_PATH.Length)
    $dest_path = Join-Path $DEST_DIR $item_relative
    # echo "$($m.FullName) => $($dest_path)"
    New-Item -ItemType File -Path $dest_path -Force | Out-Null
    Copy-Item -Path $m.FullName -Destination $dest_path -Force
}

