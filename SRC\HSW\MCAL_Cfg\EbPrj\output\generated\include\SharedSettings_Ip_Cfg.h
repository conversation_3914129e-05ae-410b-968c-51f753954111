/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef SHARED_SETTINGS_IP_CFG_H
#define SHARED_SETTINGS_IP_CFG_H

/**
*   @file       SharedSettings_Ip_Cfg.h
*   @version    5.0.0
*
*   @brief   AUTOSAR Mcu - SharedSettings configuration header file.
*   @details This file is the header containing all the necessary information for SHARED_SETTINGS
*            module configuration(s).
*
*   @addtogroup SHARED_SETTINGS_DRIVER_CONFIGURATION SharedSettings Ip Driver
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "SharedSettings_Ip_VS_0_PBcfg.h"
#include "SharedSettings_Ip_VS_Headless_PBcfg.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define SHARED_SETTINGS_IP_CFG_VENDOR_ID                      43
#define SHARED_SETTINGS_IP_CFG_AR_RELEASE_MAJOR_VERSION       4
#define SHARED_SETTINGS_IP_CFG_AR_RELEASE_MINOR_VERSION       4
#define SHARED_SETTINGS_IP_CFG_AR_RELEASE_REVISION_VERSION    0
#define SHARED_SETTINGS_IP_CFG_SW_MAJOR_VERSION               5
#define SHARED_SETTINGS_IP_CFG_SW_MINOR_VERSION               0
#define SHARED_SETTINGS_IP_CFG_SW_PATCH_VERSION               0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_0_PBcfg.h file are of the same vendor */
#if (SHARED_SETTINGS_IP_CFG_VENDOR_ID != SHARED_SETTINGS_IP_VS_0_PBCFG_VENDOR_ID)
    #error "SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_0_PBcfg.h have different vendor ids"
#endif

/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_0_PBcfg.h file are of the same Autosar version */
#if ((SHARED_SETTINGS_IP_CFG_AR_RELEASE_MAJOR_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_AR_RELEASE_MINOR_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_AR_RELEASE_REVISION_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_0_PBcfg.h are different"
#endif

/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_0_PBcfg.h file are of the same Software version */
#if ((SHARED_SETTINGS_IP_CFG_SW_MAJOR_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_SW_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_SW_MINOR_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_SW_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_SW_PATCH_VERSION != SHARED_SETTINGS_IP_VS_0_PBCFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_0_PBcfg.h are different"
#endif
/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_Headless_PBcfg.h file are of the same vendor */
#if (SHARED_SETTINGS_IP_CFG_VENDOR_ID != SHARED_SETTINGS_IP_VS_Headless_PBCFG_VENDOR_ID)
    #error "SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_Headless_PBcfg.h have different vendor ids"
#endif

/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_Headless_PBcfg.h file are of the same Autosar version */
#if ((SHARED_SETTINGS_IP_CFG_AR_RELEASE_MAJOR_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_AR_RELEASE_MINOR_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_AR_RELEASE_REVISION_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_Headless_PBcfg.h are different"
#endif

/* Check if SharedSettings_Ip_Cfg.h file and SharedSettings_Ip_VS_Headless_PBcfg.h file are of the same Software version */
#if ((SHARED_SETTINGS_IP_CFG_SW_MAJOR_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MAJOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_SW_MINOR_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_MINOR_VERSION) || \
     (SHARED_SETTINGS_IP_CFG_SW_PATCH_VERSION != SHARED_SETTINGS_IP_VS_Headless_PBCFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of SharedSettings_Ip_Cfg.h and SharedSettings_Ip_VS_Headless_PBcfg.h are different"
#endif
/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
/**
* @brief            Pre-processor switch for enabling the default error detection and reporting to the DET.
*                   The detection of default errors is configurable (ON / OFF) at pre-compile time.
* @implements SHARED_SETTINGS_IP_DEV_ERROR_DETECT_Define
*/
#define SHARED_SETTINGS_IP_DEV_ERROR_DETECT         (STD_OFF)


/**
* @brief        Support for User mode.
*               If this parameter has been configured to 'TRUE' the SharedSettings can be executed from both supervisor and user mode.
*/
#define SHARED_SETTINGS_IP_ENABLE_USER_MODE_SUPPORT  (STD_OFF)

/**
* @brief        Support for SCMI Support.
*               If this parameter has been configured to \'TRUE\' the Shared Settings can be executed from SCMI API.
*/
#define SHARED_SETTINGS_IP_SCMI_PLATFORM_SUPPORT       (STD_ON)
/** Check the driver user mode is enabled only when the MCAL_ENABLE_USER_MODE_SUPPORT is enabled */
#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
  #if (STD_ON == SHARED_SETTINGS_IP_ENABLE_USER_MODE_SUPPORT)
    #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running SharedSettings in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined.
  #endif /* (STD_ON == SHARED_SETTINGS_IP_ENABLE_USER_MODE_SUPPORT) */
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
                                             ENUMS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */
#endif /* SHARED_SETTINGS_IP_CFG_H */

