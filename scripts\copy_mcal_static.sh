#!/bin/bash

# Bash script to copy filtered items from ../MCAL_Static_G3 to SRC/HSW/MCAL_Static

# 定义要处理的目录列表（注释掉不需要的项）
names=(
    "CP_Stub/CanIf_TS_T40D11M50I0R0"
    "CP_Stub/Dem_TS_T40D11M50I0R0"
    "CP_Stub/Det_TS_T40D11M50I0R0"
    "CP_Stub/EcuM_TS_T40D11M50I0R0"
    # "CP_Stub/EthIf_TS_T40D11M50I0R0"
    # "CP_Stub/FrIf_TS_T40D11M50I0R0"
    "CP_Stub/LinIf_TS_T40D11M50I0R0"
    "CP_Stub/MemIf_TS_T40D11M50I0R0"
    # "CP_Stub/PcieIf_TS_T40D11M50I0R0"
    "CP_Stub/Rte_TS_T40D11M50I0R0"
    # "CP_Stub/WdgIf_TS_T40D11M50I0R0"
    "LLCE/Can_43_LLCE_TS_T40D11M10I10R0"
    # "LLCE/Fr_43_LLCE_TS_T40D11M10I10R0"
    "LLCE/Lin_43_LLCE_TS_T40D11M10I10R0"
    # "LLCE/Llce_Af_TS_T40D11M10I10R0"
    "RTD/Adc_TS_T40D11M50I0R0"
    "RTD/BaseNXP_TS_T40D11M50I0R0"
    # "RTD/Can_TS_T40D11M50I0R0"
    # "RTD/Crc_TS_T40D11M50I0R0"
    "RTD/Dio_TS_T40D11M50I0R0"
    # "RTD/EcuC_TS_T40D11M50I0R0"
    # "RTD/Eep_TS_T40D11M50I0R0"
    # "RTD/Eth_TS_T40D11M50I0R0"
    # "RTD/EthSwt_TS_T40D11M50I0R0"
    # "RTD/EthTrcv_TS_T40D11M50I0R0"
    # "RTD/Fee_TS_T40D11M50I0R0"
    "RTD/Fls_TS_T40D11M50I0R0"
    # "RTD/Fr_TS_T40D11M50I0R0"
    "RTD/Gpt_TS_T40D11M50I0R0"
    # "RTD/I2c_TS_T40D11M50I0R0"
    "RTD/Icu_TS_T40D11M50I0R0"
    # "RTD/Lin_TS_T40D11M50I0R0"
    # "RTD/Mcl_TS_T40D11M50I0R0"
    "RTD/Mcu_TS_T40D11M50I0R0"
    # "RTD/Ocotp_TS_T40D11M50I0R0"
    # "RTD/Ocu_TS_T40D11M50I0R0"
    # "RTD/Os_TS_T40D11M50I0R0"
    # "RTD/Pcie_TS_T40D11M50I0R0"
    "RTD/Platform_TS_T40D11M50I0R0"
    # "RTD/Pmic_TS_T40D11M50I0R0"
    "RTD/Port_TS_T40D11M50I0R0"
    "RTD/Pwm_TS_T40D11M50I0R0"
    # "RTD/Qdec_TS_T40D11M50I0R0"
    # "RTD/Resource_TS_T40D11M50I0R0"
    "RTD/Rm_TS_T40D11M50I0R0"
    # "RTD/Serdes_TS_T40D11M50I0R0"
    # "RTD/Spi_TS_T40D11M50I0R0"
    # "RTD/Thermal_TS_T40D11M50I0R0"
    "RTD/Uart_TS_T40D11M50I0R0"
    # "RTD/Wdg_43_VR5510_TS_T40D11M50I0R0"
    # "RTD/Wdg_TS_T40D11M50I0R0"
)

SOURCE_DIR="../MCAL_Static_G3"
SOURCE_DIR_PATH=$(realpath "$SOURCE_DIR")
DEST_DIR="./SRC/HSW/MCAL_Static"

echo "Copying items from $SOURCE_DIR to $DEST_DIR"
echo "================================================================="

# 确保目标目录存在
mkdir -p "$DEST_DIR"

# 定义要查找的目录名称
filters=("include" "src" "header" "build_files")

# 处理所有指定目录
for n in "${names[@]}"; do
    # 跳过空行和注释行
    if [[ -z "$n" ]] || [[ "$n" == \#* ]]; then
        continue
    fi
    
    full_path="$SOURCE_DIR_PATH/$n"
    
    # 检查目录是否存在
    if [[ ! -d "$full_path" ]]; then
        echo "警告: 目录不存在 - $full_path"
        continue
    fi
    
    # 查找并复制特定子目录
    for filter in "${filters[@]}"; do
        subdir_path="$full_path/$filter"
        
        if [[ -d "$subdir_path" ]]; then
            # 计算相对路径
            relative_path="${subdir_path#$SOURCE_DIR_PATH/}"
            dest_path="$DEST_DIR/$relative_path"
            parent_path=$(dirname "$dest_path")
            
            # 创建目标目录并复制内容
            mkdir -p "$parent_path"
            echo "copying: $subdir_path -> $parent_path"
            cp -R "$subdir_path" "$parent_path"
        fi
    done
done

# 处理.mak文件
for n in "${names[@]}"; do
    # 跳过空行和注释行
    if [[ -z "$n" ]] || [[ "$n" == \#* ]]; then
        continue
    fi
    
    full_path="$SOURCE_DIR_PATH/$n"
    
    # 查找.mak文件（最多1层深度）
    find "$full_path" -maxdepth 1 -type f -name "*.mak" | while read -r mak_file; do
        relative_path="${mak_file#$SOURCE_DIR_PATH/}"
        dest_path="$DEST_DIR/$relative_path"
        
        # 创建目标目录并复制文件
        mkdir -p "$(dirname "$dest_path")"
        echo "copying: $mak_file -> $dest_path"
        cp "$mak_file" "$dest_path"
    done
done

# 处理特定目录中的.mak文件
for dir in "LLCE" "RTD" "CP_Stub" ""; do
    full_path="$SOURCE_DIR_PATH/$dir"

    if [[ ! -d "$full_path" ]]; then
        echo "警告: 目录不存在 - $full_path"
        continue
    fi
    
    # 查找.c文件（最多1层深度）
    find "$full_path" -maxdepth 1 -type f -name "*.mak" | while read -r c_file; do
        relative_path="${c_file#$SOURCE_DIR_PATH/}"
        dest_path="$DEST_DIR/$relative_path"
       
        # 创建目标目录并复制文件
        mkdir -p "$(dirname "$dest_path")"
        echo "copying: $c_file -> $dest_path"
        cp "$c_file" "$dest_path"
    done
done

echo
echo "================================================================="
echo "Copy operation completed."
echo "Destination: $DEST_DIR"
