/**
*   @file    Lin_43_LLCE_Cfg.h
*   @version 1.0.10
*
*   @brief   AUTOSAR Lin_43_LLCE - High level header of LIN driver.
*   @details This file contains declarations of the functions defined by AutoSAR.
*
*   @addtogroup LIN_LLCE
*   @{
*/
/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : LLCE
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 1.0.10
*   Build Version        : S32_RTD_1_0_10_D2505_ASR_REL_4_4_REV_0000_20250516
*
*   Copyright 2006-2016 Freescale Semiconductor, Inc.
*   Copyright 2020-2025 NXP
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be used strictly in
*   accordance with the applicable license terms.
*   By expressly accepting such terms or by downloading, installing, activating and/or otherwise using
*   the software, you are agreeing that you have read, and that you agree to comply with and are bound
*   by, such license terms.  If you do not agree to be bound by the applicable license terms, then you
*   may not retain, install, activate or otherwise use the software.
==================================================================================================*/

#ifndef LIN_43_LLCE_CFG_H
#define LIN_43_LLCE_CFG_H

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "EcuM_Externals.h"
#include "Mcal.h"
#include "Lin_43_LLCE_VS_0_PBcfg.h"
#include "Lin_43_LLCE_VS_Headless_PBcfg.h"
#include "Llce_InterfaceLinTypes.h"
#include "OsIf.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @file           Lin_43_LLCE_Cfg.h
*/

/* Inclusion of incompatible header files shall be avoided */

#define LIN_43_LLCE_CFG_VENDOR_ID                    43
#define LIN_43_LLCE_CFG_AR_RELEASE_MAJOR_VERSION     4
#define LIN_43_LLCE_CFG_AR_RELEASE_MINOR_VERSION     4
#define LIN_43_LLCE_CFG_AR_RELEASE_REVISION_VERSION  0
#define LIN_43_LLCE_CFG_SW_MAJOR_VERSION             1
#define LIN_43_LLCE_CFG_SW_MINOR_VERSION             0
#define LIN_43_LLCE_CFG_SW_PATCH_VERSION             10
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lin_VS_0_PBcfg.h */
#if (LIN_43_LLCE_CFG_VENDOR_ID != LIN_43_LLCE_VS_0_PBCFG_VENDOR_ID)
    #error "Lin_Cfg.h and Lin_VS_0_PBcfg.h have different vendor ids"
#endif
#if ((LIN_43_LLCE_CFG_AR_RELEASE_MAJOR_VERSION    != LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_43_LLCE_CFG_AR_RELEASE_MINOR_VERSION    != LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_43_LLCE_CFG_AR_RELEASE_REVISION_VERSION != LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lin_Cfg.h and Lin_VS_0_PBcfg.h are different"
#endif
#if ((LIN_43_LLCE_CFG_SW_MAJOR_VERSION != LIN_43_LLCE_VS_0_PBCFG_SW_MAJOR_VERSION) || \
     (LIN_43_LLCE_CFG_SW_MINOR_VERSION != LIN_43_LLCE_VS_0_PBCFG_SW_MINOR_VERSION) || \
     (LIN_43_LLCE_CFG_SW_PATCH_VERSION != LIN_43_LLCE_VS_0_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Cfg.h and Lin_VS_0_PBcfg.h are different"
#endif
/* Checks against Lin_VS_Headless_PBcfg.h */
#if (LIN_43_LLCE_CFG_VENDOR_ID != LIN_43_LLCE_VS_Headless_PBCFG_VENDOR_ID)
    #error "Lin_Cfg.h and Lin_VS_Headless_PBcfg.h have different vendor ids"
#endif
#if ((LIN_43_LLCE_CFG_AR_RELEASE_MAJOR_VERSION    != LIN_43_LLCE_VS_Headless_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_43_LLCE_CFG_AR_RELEASE_MINOR_VERSION    != LIN_43_LLCE_VS_Headless_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_43_LLCE_CFG_AR_RELEASE_REVISION_VERSION != LIN_43_LLCE_VS_Headless_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lin_Cfg.h and Lin_VS_Headless_PBcfg.h are different"
#endif
#if ((LIN_43_LLCE_CFG_SW_MAJOR_VERSION != LIN_43_LLCE_VS_Headless_PBCFG_SW_MAJOR_VERSION) || \
     (LIN_43_LLCE_CFG_SW_MINOR_VERSION != LIN_43_LLCE_VS_Headless_PBCFG_SW_MINOR_VERSION) || \
     (LIN_43_LLCE_CFG_SW_PATCH_VERSION != LIN_43_LLCE_VS_Headless_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Cfg.h and Lin_VS_Headless_PBcfg.h are different"
#endif


#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
  /* Check if the source file and EcuM_Cbk header file are of the same version */
  #if ((LIN_43_LLCE_CFG_AR_RELEASE_MAJOR_VERSION != ECUM_CBK_AR_RELEASE_MAJOR_VERSION) || \
       (LIN_43_LLCE_CFG_AR_RELEASE_MINOR_VERSION != ECUM_CBK_AR_RELEASE_MINOR_VERSION) \
      )
    #error "AutoSar Version Numbers of Lin_43_LLCE_Cfg.h and EcuM_Externals.h are different"
  #endif
  /* Check if current file and Mcal.h header file are of the same Autosar version */
    #if ((LIN_43_LLCE_CFG_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (LIN_43_LLCE_CFG_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION) \
        )
        #error "AutoSar Version Numbers of Lin_43_LLCE_Cfg.h and Mcal.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/**
* @brief   No of Channels configured.
*
*
* @api
*/
#define LIN_43_LLCE_HW_MAX_MODULES 4U

/**
* @brief          Lin Master Node Used
* @details        When LinGlobalConfig/LinChannel/LinNodeType contains at least one MASTER channel.
*
* @api
*/
#define LIN_43_LLCE_MASTER_NODE_USED (STD_ON) /* Used Master Node */ 
/**
* @brief          Lin Slave Node Used
* @details        When LinGlobalConfig/LinChannel/LinNodeType contains at least one SLAVE channel.
*
* @api
*/
#define LIN_43_LLCE_SLAVE_NODE_USED (STD_ON) /* Used Slave Node */ 
/**
* @brief   This will set the timer source for osif that will be used for timeout.
*
* @api
*/
#define  LIN_43_LLCE_SERVICE_TIMEOUT_TYPE    OSIF_COUNTER_DUMMY

/**
* @brief   Number of loops before returning LIN_43_LLCE_E_TIMEOUT.
*
* @api
*/
#define  LIN_43_LLCE_TIMEOUT_DURATION ((uint32)65000U)

/**
* @brief   Switches the Default Error Detection and Notification ON or OFF.
*
* @api
*/
#define LIN_43_LLCE_DEV_ERROR_DETECT (STD_ON) /* Enable Default Error Detection */

/**
* @brief          Support for version info API.
* @details        Switches the Lin_43_LLCE_GetVersionInfo() API ON or OFF.
*
* @api
*/
#define LIN_43_LLCE_VERSION_INFO_API (STD_ON) /* Enable API Lin_43_LLCE_GetVersionInfo */


/**
* @brief          Link Lin channels symbolic names with Lin channel IDs.
* @details        Link Lin channels symbolic names with Lin channel IDs.
*
* @api
*/

#define LinHWCh_0    0U
                
#define LinHWCh_1    1U
                
#define LinHWCh_2    2U
                
#define LinHWCh_3    3U
                

/**
* @brief          Symbolic names for configured channels.
* @details        Symbolic names for configured channels.
*
* @api
*/
#define LinConf_43_LLCE_LinChannelConfiguration_LinChannel_0         0
#define LinConf_43_LLCE_LinChannelConfiguration_LinChannel_1         1
#define LinConf_43_LLCE_LinChannelConfiguration_LinChannel_2         2
#define LinConf_43_LLCE_LinChannelConfiguration_LinChannel_3         3

/**
* @brief   Switches the Production Error Detection and Notification OFF
*
* @api
*/


#define LIN_43_LLCE_DISABLE_DEM_REPORT_ERROR_STATUS (STD_ON) /* Disable Production Error Detection */

#define LIN_43_LLCE_PRECOMPILE_SUPPORT (STD_OFF)


/**
* @brief          None EcuMWakeUpSource was referrd when LinChannelWakeupSupport is disable
*/
#define LIN_43_LLCE_NONE_ECUM_WAKEUP_SOURCE_REF (uint32)0U
    
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define LIN_43_LLCE_CONFIG_EXT \
 LIN_43_LLCE_CONFIG_VS_0_PB \
 LIN_43_LLCE_CONFIG_VS_Headless_PB \



/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/
/**
* @brief          LIN channel configuration type structure.
* @details        This is the type of the external data structure containing
*                 the overall initialization data for one LIN Channel.
*                 A pointer to such a structure is provided to the LIN channel
*                 initialization routine for configuration of the LIN hardware
*                 channel.
*
* @api
*/
typedef struct
{
    uint8 u8LinChannelID;                     /**< @brief Lin Channel ID */
    uint8 u8LinHwChannel;                     /**< @brief LIN Hardware Channel.*/
    const Llce_Lin_NodeType eLinNodeType;         /*!< @brief LIN Node Type.*/
    uint8 u8LinChannelWakeupSupport;          /**< @brief Is wake-up supported by the LIN channel ?.*/
    uint8 u8LinChannelDisableFrameTimeout;    /**< @brief Disable frame timeout for this channel.*/
    EcuM_WakeupSourceType LinChannelEcuMWakeupSource;    /**< @brief [LIN098] This parameter contains a reference to the Wakeup Source for this controller as defined in the ECU State Manager.*/
} Lin_43_LLCE_StaticConfig_ChannelConfigType;

/**
* @brief          LIN channel configuration type structure.
* @details        This is the type of the external data structure containing
*                 the overall initialization data for one LIN Channel.
*                 A pointer to such a structure is provided to the LIN channel
*                 initialization routine for configuration of the LIN hardware
*                 channel.
*
* @api
*/
typedef struct
{
    const Lin_43_LLCE_StaticConfig_ChannelConfigType * pChannelConfigPC;  /* @brief Pointer to precompile configuration structure */
    uint32 u32LinBaudRateRegValue;            /**< @brief LIN baudrate value.*/
    uint8 u8LinChannelBrkLengthMaster;        /*!< @brief These bits indicate the Break length in Master mode.*/
    uint8 u8LinChannelBrkLengthSlave;         /*!< @brief These bits indicate the Break length in Slave mode.*/
    uint8 u8ResponseTimeout;                  /*!< @brief Response timeout value LINTOCR[RTO].*/
    uint8 u8HeaderTimeout;                    /*!< @brief Header timeout value LINTOCR[HTO].*/
} Lin_43_LLCE_ChannelConfigType;

/**
* @brief          LIN driver configuration type structure.
* @details        This is the type of the pointer to the external data
*                 LIN Channels.
*                 A pointer of such a structure is provided to the LIN driver
*                 initialization routine for configuration of the LIN hardware
*                 channel.
*
* @api
*
* @implements Lin_ConfigType_struct
*/
typedef struct
{
    /**
     * @brief     Hardware channel.
     * @details   Constant pointer of the constant external data
     *            structure containing the overall initialization data
     *            for all the LIN Channels.
     */
    const Lin_43_LLCE_ChannelConfigType * const pLin_Channel[LIN_43_LLCE_HW_MAX_MODULES];
} Lin_43_LLCE_ConfigType;

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define LIN_43_LLCE_START_SEC_CONST_UNSPECIFIED
#include "Lin_43_LLCE_MemMap.h"

extern const Lin_43_LLCE_StaticConfig_ChannelConfigType Lin_43_LLCE_LinChannel_0_PC;
    extern const Lin_43_LLCE_StaticConfig_ChannelConfigType Lin_43_LLCE_LinChannel_1_PC;
    extern const Lin_43_LLCE_StaticConfig_ChannelConfigType Lin_43_LLCE_LinChannel_2_PC;
    extern const Lin_43_LLCE_StaticConfig_ChannelConfigType Lin_43_LLCE_LinChannel_3_PC;
    
#if (LIN_43_LLCE_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
extern const Mcal_DemErrorType Lin_43_LLCE_E_TimeoutCfg;
#endif /* LIN_43_LLCE_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF */

#define LIN_43_LLCE_STOP_SEC_CONST_UNSPECIFIED
#include "Lin_43_LLCE_MemMap.h"

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#endif /* LIN_43_LLCE_CFG_H */

#ifdef __cplusplus
}
#endif
