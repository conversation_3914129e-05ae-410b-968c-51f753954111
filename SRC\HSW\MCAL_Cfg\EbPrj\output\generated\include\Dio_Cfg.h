/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : SIUL2
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef DIO_CFG_H
#define DIO_CFG_H

/**
*   @file Dio_Cfg.h
*   @implements Dio_Cfg.h_Artifact
*
*   @defgroup DIO_CFG Dio Cfg
*   @{
*/

#ifdef __cplusplus
extern "C" {
#endif

/*=================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
=================================================================================================*/
#include "StandardTypes.h"
#include "Siul2_Dio_Ip_Cfg.h"

/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/
#define DIO_VENDOR_ID_CFG_H                   43
#define DIO_AR_RELEASE_MAJOR_VERSION_CFG_H    4
#define DIO_AR_RELEASE_MINOR_VERSION_CFG_H    4
#define DIO_AR_RELEASE_REVISION_VERSION_CFG_H 0
#define DIO_SW_MAJOR_VERSION_CFG_H            5
#define DIO_SW_MINOR_VERSION_CFG_H            0
#define DIO_SW_PATCH_VERSION_CFG_H            0

/*=================================================================================================
*                                     FILE VERSION CHECKS
=================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* StandardTypes.h version check start */
    #if ((DIO_AR_RELEASE_MAJOR_VERSION_CFG_H != STD_AR_RELEASE_MAJOR_VERSION) ||   \
        (DIO_AR_RELEASE_MINOR_VERSION_CFG_H != STD_AR_RELEASE_MINOR_VERSION)       \
        )
        #error "AUTOSAR Version Numbers of Dio_Cfg.h and StandardTypes.h are different"
    #endif
    /* StandardTypes.h version check end */
#endif

/* Check if Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h files are of the same vendor.*/
#if (DIO_VENDOR_ID_CFG_H != SIUL2_DIO_IP_VENDOR_ID_CFG_H)
    #error "Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h have different vendor ids"
#endif
/* Check if Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h files are of the same Autosar version.*/
#if ((DIO_AR_RELEASE_MAJOR_VERSION_CFG_H    != SIUL2_DIO_IP_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
     (DIO_AR_RELEASE_MINOR_VERSION_CFG_H    != SIUL2_DIO_IP_AR_RELEASE_MINOR_VERSION_CFG_H) || \
     (DIO_AR_RELEASE_REVISION_VERSION_CFG_H != SIUL2_DIO_IP_AR_RELEASE_REVISION_VERSION_CFG_H) \
    )
    #error "AutoSar Version Numbers of Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h are different"
#endif
/* Check if Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h files are of the same Software version.*/
#if ((DIO_SW_MAJOR_VERSION_CFG_H != SIUL2_DIO_IP_SW_MAJOR_VERSION_CFG_H) || \
     (DIO_SW_MINOR_VERSION_CFG_H != SIUL2_DIO_IP_SW_MINOR_VERSION_CFG_H) || \
     (DIO_SW_PATCH_VERSION_CFG_H != SIUL2_DIO_IP_SW_PATCH_VERSION_CFG_H)    \
    )
    #error "Software Version Numbers of Dio_Cfg.h and Siul2_Dio_Ip_Cfg.h are different"
#endif

/*=================================================================================================
*                                          CONSTANTS
=================================================================================================*/


/**
* @brief          Enable or Disable Development Error Detection.
*
* @implements     DIO_DEV_ERROR_DETECT_define
*/
#define DIO_DEV_ERROR_DETECT    (STD_OFF)

/**
* @brief          Function @p Dio_GetVersionInfo() enable switch.
*
* @implements     DIO_VERSION_INFO_API_define
*/
#define DIO_VERSION_INFO_API    (STD_OFF)

/**
* @brief          Function @p Dio_FlipChannel() enable switch.
*/
#define DIO_FLIP_CHANNEL_API    (STD_ON)

/**
* @brief          Function @p Dio_MaskedWritePort() enable switch.
*/
#define DIO_MASKEDWRITEPORT_API (STD_OFF)

/**
* @brief          Function @p Dio_ScmiMaskedReadPort() enable switch.
*/
#define DIO_SCMIMASKEDREADPORT_API (STD_OFF)

/**
* @brief          Function @p Dio_ScmiWriteChannelGroup() enable switch.
*/
#define DIO_SCMIWRITECHANNELGROUP_API    (STD_OFF)

/**
* @brief          Platform specific define stating if mapping of port bits over port pins is reversed.
*/
#define DIO_REVERSED_MAPPING_OF_PORT_BITS_OVER_PORT_PINS    (STD_ON)

/**
* @brief          Reversed port functionality enable switch.
*
* @implements DIO_REVERSEPORTBITS_define
*/
#define DIO_REVERSEPORTBITS     (STD_ON)


/**
* @brief          Undefined pins masking enable switch.
*/
#define DIO_READZERO_UNDEFINEDPORTS (STD_OFF)



/**
* @brief Enable/Disable Multicore function from the driver
*/
#define DIO_MULTICORE_ENABLED          (STD_OFF)

/**
* @brief           Enable/Disable the Scmi Platform Support.
*
* @implements DIO_SCMI_PLATFORM_SUPPORT_define
*/
#define DIO_SCMI_PLATFORM_SUPPORT          (STD_ON)

/**
* @brief          Number of implemented ports.
*
* @note           Used for channel, port and channel group validation.
*/
#define DIO_NUM_PORTS_U16               ((uint16)0xcU)

/**
* @brief          The number of partition on the port
*
* @note           Used for port validation.
*/
#define DIO_PORT_PARTITION_U16          ((uint16)12U)

/**
* @brief          Number of channels available on the implemented ports.
*
* @note           Used for channel validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_NUM_CHANNELS_U16            ((uint16)190U)
#endif


/**
* @brief The number of partition on the channel.
*
* @note           Used for channel validation.
*/
#define DIO_CHANNEL_PARTITION_U16            ((uint16)191U)

/**
* @brief          Mask representing no available channels on a port.
*
* @note           Used for channel validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_NO_AVAILABLE_CHANNELS_U16   ((Dio_PortLevelType)0x0U)
#endif

/**
* @brief          Mask representing the maximum valid offset for a channel group.
*
* @note           Used for channel group validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_MAX_VALID_OFFSET_U8         ((uint8)0xFU)
#endif

/**
*   @brief   Enables or disables the access to a hardware register from user mode
*            USER_MODE_SOFT_LOCKING:        All reads to hw registers will be done via REG_PROT, user mode access
*            SUPERVISOR_MODE_SOFT_LOCKING:  Locks the access to the registers only for supervisor mode
*
*   @note    Currently, no register protection mechanism is used for Dio driver.
*/
#define DIO_USER_MODE_SOFT_LOCKING      (STD_OFF)

/**
* @brief          Dio driver Pre-Compile configuration switch.
*/
#define DIO_PRECOMPILE_SUPPORT


/**
* @brief Support for User mode.
*        If this parameter has been configured to 'STD_ON', the Dio driver code can be executed from both supervisor and user mode.
*
*/

#define DIO_ENABLE_USER_MODE_SUPPORT   (STD_OFF)

/**
* @brief Support for REG_PROT in SIUL2 IP.
*        If the current platform implements REG_PROT for SIUL2 IP, this parameter will be defined, and will enable REG_PROT configuration for SIUL2 IP in DIO drvier
*/
#define DIO_SIUL2_REG_PROT_AVAILABLE    (STD_OFF)


#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
 #ifdef DIO_ENABLE_USER_MODE_SUPPORT
  #if (STD_ON == DIO_ENABLE_USER_MODE_SUPPORT)
    #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Dio in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined
  #endif /* (STD_ON == DIO_ENABLE_USER_MODE_SUPPORT) */
 #endif /* ifdef DIO_ENABLE_USER_MODE_SUPPORT*/
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */

/**
* @brief Number of SIUL2 instances on the platform.
*/
#define DIO_NUM_SIUL2_INSTANCES_U8      ((uint8)2U)

/**
* @brief List of identifiers for each of the SIUL2 instances on the platform
*/
#define DIO_SIUL2_0_U8        ((uint8)0)
#define DIO_SIUL2_1_U8        ((uint8)1)

/*=================================================================================================
*                                      DEFINES AND MACROS
=================================================================================================*/


/**
* @brief          Symbolic name for the configuration Dio_ConfigPC.
*
*/
#define Dio_ConfigPC    (Dio_Config)

/* ========== DioConfig ========== */

/* ---------- DioPort_PA ---------- */

/**
* @brief          Symbolic name for the port DioPort_PA.
*
*/
#define DioConf_DioPort_DioPort_PA  ((uint8)0x00U)



/**
* @brief          Symbolic name for the channel PA_6_CCU_LIN3_SLP.
*
*/
#define  DioConf_DioChannel_PA_6_CCU_LIN3_SLP ((uint16)0x0006U)



/**
* @brief          Symbolic name for the channel PA_7_CCU_LIN4_SLP.
*
*/
#define  DioConf_DioChannel_PA_7_CCU_LIN4_SLP ((uint16)0x0007U)



/**
* @brief          Symbolic name for the channel PA_8.
*
*/
#define  DioConf_DioChannel_PA_8 ((uint16)0x0008U)



/**
* @brief          Symbolic name for the channel PA_9_CZT_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PA_9_CZT_CANFD_STB ((uint16)0x0009U)



/**
* @brief          Symbolic name for the channel PA_11_SER1_POW_CTL.
*
*/
#define  DioConf_DioChannel_PA_11_SER1_POW_CTL ((uint16)0x000bU)



/**
* @brief          Symbolic name for the channel PA_12_SER2_POW_CTL.
*
*/
#define  DioConf_DioChannel_PA_12_SER2_POW_CTL ((uint16)0x000cU)

/* ---------- DioPort_PB ---------- */

/**
* @brief          Symbolic name for the port DioPort_PB.
*
*/
#define DioConf_DioPort_DioPort_PB  ((uint8)0x01U)



/**
* @brief          Symbolic name for the channel PB_1_CCU_LIN1_SLP.
*
*/
#define  DioConf_DioChannel_PB_1_CCU_LIN1_SLP ((uint16)0x0011U)



/**
* @brief          Symbolic name for the channel PB_2_CCU_LIN2_SLP.
*
*/
#define  DioConf_DioChannel_PB_2_CCU_LIN2_SLP ((uint16)0x0012U)



/**
* @brief          Symbolic name for the channel PB_4_CCU_CANFD2_STB.
*
*/
#define  DioConf_DioChannel_PB_4_CCU_CANFD2_STB ((uint16)0x0014U)



/**
* @brief          Symbolic name for the channel PB_5_CCU_CANFD4_STB.
*
*/
#define  DioConf_DioChannel_PB_5_CCU_CANFD4_STB ((uint16)0x0015U)



/**
* @brief          Symbolic name for the channel PB_6_CHG_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PB_6_CHG_CANFD_STB ((uint16)0x0016U)



/**
* @brief          Symbolic name for the channel PB_7_CCU_CANFD3_STB.
*
*/
#define  DioConf_DioChannel_PB_7_CCU_CANFD3_STB ((uint16)0x0017U)



/**
* @brief          Symbolic name for the channel PB_8_CZF_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PB_8_CZF_CANFD_STB ((uint16)0x0018U)



/**
* @brief          Symbolic name for the channel PB_11_CZL_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PB_11_CZL_CANFD_STB ((uint16)0x001bU)



/**
* @brief          Symbolic name for the channel PB_13_PUB_CANFD1_STB.
*
*/
#define  DioConf_DioChannel_PB_13_PUB_CANFD1_STB ((uint16)0x001dU)



/**
* @brief          Symbolic name for the channel PB_14_PUB_CANFD2_STB.
*
*/
#define  DioConf_DioChannel_PB_14_PUB_CANFD2_STB ((uint16)0x001eU)

/* ---------- DioPort_PC ---------- */

/**
* @brief          Symbolic name for the port DioPort_PC.
*
*/
#define DioConf_DioPort_DioPort_PC  ((uint8)0x02U)



/**
* @brief          Symbolic name for the channel PC_13.
*
*/
#define  DioConf_DioChannel_PC_13 ((uint16)0x002dU)

/* ---------- DioPort_PD ---------- */

/**
* @brief          Symbolic name for the port DioPort_PD.
*
*/
#define DioConf_DioPort_DioPort_PD  ((uint8)0x03U)



/**
* @brief          Symbolic name for the channel PD_9_PHY_0V9_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PD_9_PHY_0V9_CTL_1V8 ((uint16)0x0039U)



/**
* @brief          Symbolic name for the channel PD_11.
*
*/
#define  DioConf_DioChannel_PD_11 ((uint16)0x003bU)

/* ---------- DioPort_PE ---------- */

/**
* @brief          Symbolic name for the port DioPort_PE.
*
*/
#define DioConf_DioPort_DioPort_PE  ((uint8)0x04U)



/**
* @brief          Symbolic name for the channel PE_0_BRAKE_PEDAL_SW_NO_MCU_1V8.
*
*/
#define  DioConf_DioChannel_PE_0_BRAKE_PEDAL_SW_NO_MCU_1V8 ((uint16)0x0040U)



/**
* @brief          Symbolic name for the channel PE_2_SWT_WAKE_1V8.
*
*/
#define  DioConf_DioChannel_PE_2_SWT_WAKE_1V8 ((uint16)0x0042U)



/**
* @brief          Symbolic name for the channel PE_3_SWT_0V9_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PE_3_SWT_0V9_CTL_1V8 ((uint16)0x0043U)



/**
* @brief          Symbolic name for the channel PE_4.
*
*/
#define  DioConf_DioChannel_PE_4 ((uint16)0x0044U)



/**
* @brief          Symbolic name for the channel PE_5_TBX_PHY_WAKE_1V8.
*
*/
#define  DioConf_DioChannel_PE_5_TBX_PHY_WAKE_1V8 ((uint16)0x0045U)



/**
* @brief          Symbolic name for the channel PE_7_SECU_3V3_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PE_7_SECU_3V3_CTL_1V8 ((uint16)0x0047U)



/**
* @brief          Symbolic name for the channel PE_8_SECU_RST_1V8.
*
*/
#define  DioConf_DioChannel_PE_8_SECU_RST_1V8 ((uint16)0x0048U)



/**
* @brief          Symbolic name for the channel PE_9_SWT_RST_1V8.
*
*/
#define  DioConf_DioChannel_PE_9_SWT_RST_1V8 ((uint16)0x0049U)



/**
* @brief          Symbolic name for the channel PE_10_TBX_PHY_RST_1V8.
*
*/
#define  DioConf_DioChannel_PE_10_TBX_PHY_RST_1V8 ((uint16)0x004aU)



/**
* @brief          Symbolic name for the channel PE_11.
*
*/
#define  DioConf_DioChannel_PE_11 ((uint16)0x004bU)



/**
* @brief          Symbolic name for the channel PE_12.
*
*/
#define  DioConf_DioChannel_PE_12 ((uint16)0x004cU)



/**
* @brief          Symbolic name for the channel PE_13_SWT_DIS_1V8.
*
*/
#define  DioConf_DioChannel_PE_13_SWT_DIS_1V8 ((uint16)0x004dU)



/**
* @brief          Symbolic name for the channel PE_14_BAT2_ADC_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PE_14_BAT2_ADC_CTL_1V8 ((uint16)0x004eU)



/**
* @brief          Symbolic name for the channel PE_15.
*
*/
#define  DioConf_DioChannel_PE_15 ((uint16)0x004fU)

/* ---------- DioPort_PF ---------- */

/**
* @brief          Symbolic name for the port DioPort_PF.
*
*/
#define DioConf_DioPort_DioPort_PF  ((uint8)0x05U)



/**
* @brief          Symbolic name for the channel PF_0.
*
*/
#define  DioConf_DioChannel_PF_0 ((uint16)0x0050U)



/**
* @brief          Symbolic name for the channel PF_1.
*
*/
#define  DioConf_DioChannel_PF_1 ((uint16)0x0051U)



/**
* @brief          Symbolic name for the channel PF_2.
*
*/
#define  DioConf_DioChannel_PF_2 ((uint16)0x0052U)



/**
* @brief          Symbolic name for the channel PF_3_IG1_MCU_1V8.
*
*/
#define  DioConf_DioChannel_PF_3_IG1_MCU_1V8 ((uint16)0x0053U)



/**
* @brief          Symbolic name for the channel PF_4_OBD_ACT_MCU_1V8.
*
*/
#define  DioConf_DioChannel_PF_4_OBD_ACT_MCU_1V8 ((uint16)0x0054U)



/**
* @brief          Symbolic name for the channel PF_15.
*
*/
#define  DioConf_DioChannel_PF_15 ((uint16)0x005fU)

/* ---------- DioPort_PG ---------- */

/**
* @brief          Symbolic name for the port DioPort_PG.
*
*/
#define DioConf_DioPort_DioPort_PG  ((uint8)0x06U)



/**
* @brief          Symbolic name for the channel PG_1_NORFLASH_RST_1V8.
*
*/
#define  DioConf_DioChannel_PG_1_NORFLASH_RST_1V8 ((uint16)0x0061U)



/**
* @brief          Symbolic name for the channel PG_2_BMC_PWM_DIG_1V8.
*
*/
#define  DioConf_DioChannel_PG_2_BMC_PWM_DIG_1V8 ((uint16)0x0062U)



/**
* @brief          Symbolic name for the channel PG_3_PHY_1V8_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PG_3_PHY_1V8_CTL_1V8 ((uint16)0x0063U)



/**
* @brief          Symbolic name for the channel PG_5_CAN_5V0_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PG_5_CAN_5V0_CTL_1V8 ((uint16)0x0065U)

/* ---------- DioPort_PH ---------- */

/**
* @brief          Symbolic name for the port DioPort_PH.
*
*/
#define DioConf_DioPort_DioPort_PH  ((uint8)0x07U)



/**
* @brief          Symbolic name for the channel PH_0_VDD_EFUSE_POW__CTL_1V8.
*
*/
#define  DioConf_DioChannel_PH_0_VDD_EFUSE_POW__CTL_1V8 ((uint16)0x0070U)



/**
* @brief          Symbolic name for the channel PH_5.
*
*/
#define  DioConf_DioChannel_PH_5 ((uint16)0x0075U)



/**
* @brief          Symbolic name for the channel PH_6.
*
*/
#define  DioConf_DioChannel_PH_6 ((uint16)0x0076U)



/**
* @brief          Symbolic name for the channel PH_7_BRAKE_PEDAL_SW_NC_MCU_1V8.
*
*/
#define  DioConf_DioChannel_PH_7_BRAKE_PEDAL_SW_NC_MCU_1V8 ((uint16)0x0077U)



/**
* @brief          Symbolic name for the channel PH_8_PHY_3V3_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PH_8_PHY_3V3_CTL_1V8 ((uint16)0x0078U)



/**
* @brief          Symbolic name for the channel PH_9_SWT_3V3_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PH_9_SWT_3V3_CTL_1V8 ((uint16)0x0079U)



/**
* @brief          Symbolic name for the channel PH_10_BAT1_ADC_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PH_10_BAT1_ADC_CTL_1V8 ((uint16)0x007aU)

/* ---------- DioPort_PJ ---------- */

/**
* @brief          Symbolic name for the port DioPort_PJ.
*
*/
#define DioConf_DioPort_DioPort_PJ  ((uint8)0x09U)



/**
* @brief          Symbolic name for the channel PJ_0_SRS_PWM_POW_CTL_1V8.
*
*/
#define  DioConf_DioChannel_PJ_0_SRS_PWM_POW_CTL_1V8 ((uint16)0x0090U)



/**
* @brief          Symbolic name for the channel PJ_3_CZR_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PJ_3_CZR_CANFD_STB ((uint16)0x0093U)

/* ---------- DioPort_PK ---------- */

/**
* @brief          Symbolic name for the port DioPort_PK.
*
*/
#define DioConf_DioPort_DioPort_PK  ((uint8)0x0aU)



/**
* @brief          Symbolic name for the channel PK_11_IDC_CANFD_STP.
*
*/
#define  DioConf_DioChannel_PK_11_IDC_CANFD_STP ((uint16)0x00abU)

/* ---------- DioPort_PL ---------- */

/**
* @brief          Symbolic name for the port DioPort_PL.
*
*/
#define DioConf_DioPort_DioPort_PL  ((uint8)0x0bU)



/**
* @brief          Symbolic name for the channel PL_1_IDC_CANFD_STB.
*
*/
#define  DioConf_DioChannel_PL_1_IDC_CANFD_STB ((uint16)0x00b1U)



/**
* @brief          Symbolic name for the channel PL_2_OBD_CAN_STB.
*
*/
#define  DioConf_DioChannel_PL_2_OBD_CAN_STB ((uint16)0x00b2U)



/**
* @brief          Symbolic name for the channel PL_4_TBOX_CAN_STB.
*
*/
#define  DioConf_DioChannel_PL_4_TBOX_CAN_STB ((uint16)0x00b4U)



/**
* @brief          Symbolic name for the channel PL_5_CCU_CANFD1_STB.
*
*/
#define  DioConf_DioChannel_PL_5_CCU_CANFD1_STB ((uint16)0x00b5U)



/**
* @brief          Symbolic name for the channel PL_14.
*
*/
#define  DioConf_DioChannel_PL_14 ((uint16)0x00beU)


/*=================================================================================================
*                                             ENUMS
=================================================================================================*/
#if (STD_OFF == DIO_SCMI_PLATFORM_SUPPORT)
typedef enum
{
    DIO_READ_ONLY,
    DIO_WRITE_ONLY,
    DIO_READ_WRITE,
    DIO_NO_ACCESS
} DioPortAccessType;
#endif

/*=================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
=================================================================================================*/

/**
* @brief          Type of a DIO port representation.
*
* @implements     Dio_PortType_typedef
*/
typedef uint8 Dio_PortType;

/**
* @brief          Type of a DIO channel representation.
*
* @implements     Dio_ChannelType_typedef
*/
typedef uint16 Dio_ChannelType;

/**
* @brief          Type of a DIO port levels representation.
*
* @implements     Dio_PortLevelType_typedef
*/
typedef uint16 Dio_PortLevelType;

/**
* @brief          Type of a DIO channel levels representation.
*
* @implements     Dio_LevelType_typedef
*/
typedef uint8 Dio_LevelType;

/**
* @brief          Type of a DIO channel group representation.
*
* @implements     Dio_ChannelGroupType_struct
*/
typedef struct
{
    Dio_PortType      port;      /**< @brief Port identifier.  */
    uint8             u8offset;    /**< @brief Bit offset within the port. */
    Dio_PortLevelType mask;      /**< @brief Group mask. */
} Dio_ChannelGroupType;

/**
* @brief          Type of a DIO configuration structure.
*
* @note           In this implementation there is no need for a configuration
*                 structure there is only a dummy field, it is recommended
*                 to initialize this field to zero.
*
* @implements     Dio_ConfigType_struct
*/
typedef struct
{
    uint8 u8NumChannelGroups; /**< @brief Number of channel groups in configuration */
    const Dio_ChannelGroupType * pChannelGroupList;     /**< @brief
                                               Pointer to list of channel groups in configuration */
    const uint32 * pau32Dio_ChannelToPartitionMap;      /**< @brief Pointer to channel to partition mapping */
    const uint32 * pau32Dio_PortToPartitionMap;         /**< @brief Pointer to port to partition mapping */
} Dio_ConfigType;

/*=================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
=================================================================================================*/
#define DIO_START_SEC_CONST_8
#include "Dio_MemMap.h"
/**
* @brief          List of channel groups mapping for Use Scmi.
*/
#if (STD_OFF == DIO_SCMI_PLATFORM_SUPPORT)
extern const uint8 Dio_ScmiPortMapping[DIO_NUM_PORTS_U16];
#endif/*(STD_OFF == DIO_SCMI_PLATFORM_SUPPORT)*/
#define DIO_STOP_SEC_CONST_8
#include "Dio_MemMap.h"

#define DIO_START_SEC_CONST_8
#include "Dio_MemMap.h"
/**
* @brief Array of values storing the SIUL2 instance each port on the platform belongs to
*/
extern const uint8 Dio_au8PortSiul2Instance[DIO_NUM_PORTS_U16];

/**
* @brief Array of values storing the offset PORT0 has inside the SIUL2 instance it
*        belongs to
*/
extern const uint8 Dio_au8Port0OffsetInSiul2Instance[DIO_NUM_SIUL2_INSTANCES_U8];

#define DIO_STOP_SEC_CONST_8
#include "Dio_MemMap.h"


#define DIO_START_SEC_CONST_16
#include "Dio_MemMap.h"

/**
* @brief Array of bitmaps of output pins available per port
*/
extern const Dio_PortLevelType Dio_aAvailablePinsForWrite[DIO_NUM_PORTS_U16];

/**
* @brief Array of bitmaps of input pins available per port
*/
extern const Dio_PortLevelType Dio_aAvailablePinsForRead[DIO_NUM_PORTS_U16];

#define DIO_STOP_SEC_CONST_16
#include "Dio_MemMap.h"



#define DIO_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dio_MemMap.h"

/**
* @brief            Array containing hardware access policies for Port
*/
#if (STD_OFF == DIO_SCMI_PLATFORM_SUPPORT)
extern const DioPortAccessType Dio_HwAccessMapping[DIO_NUM_PORTS_U16];
#endif

#define DIO_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dio_MemMap.h"
/*=================================================================================================
*                                    FUNCTION PROTOTYPES
=================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif  /* DIO_CFG_H */

