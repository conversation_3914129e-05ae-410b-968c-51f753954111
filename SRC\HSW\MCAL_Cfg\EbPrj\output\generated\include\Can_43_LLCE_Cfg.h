/**
*   @file    Can_43_LLCE_Cfg.h
*   @version 1.0.10
*
*   @brief   AUTOSAR Can_43_LLCE - module interface
*   @details Configuration settings generated by user settings.
*
*   @addtogroup CAN_LLCE
*   @{
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : LLCE
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 1.0.10
*   Build Version        : S32_RTD_1_0_10_D2505_ASR_REL_4_4_REV_0000_20250516
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/
/*
@brief   (CAN023) The consistency of the configuration must be checked by the configuration tool(s).
@brief   (CAN022) The code configurator of the Can module is CAN controller specific.
         If the CAN controller is sited on-chip, the code generation tool for the Can module is Controller specific.
         If the CAN controller is an external device the generation tool must not be Controller specific.
@brief   (CAN024) The valid values that can be configured are hardware dependent.
         Therefore the rules and constraints can't be given in the standard.
         The configuration tool is responsible to do a static configuration checking, also regarding dependencies between modules (i.e. Port driver, MCU driver etc.)
*/
/*
* @file    Can_43_LLCE_Cfg.h
*/

#ifndef CAN_43_LLCE_CFG_H
#define CAN_43_LLCE_CFG_H

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Std_Types.h"
#include "Soc_Ips.h"
#include "OsIf.h"

#include "Can_43_LLCE_VS_0_PBcfg.h"
#include "Can_43_LLCE_VS_Headless_PBcfg.h"


/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/*
* @file           Can_43_LLCE_Cfg.h
*/
#define CAN_43_LLCE_VENDOR_ID_CFG_H                     43
#define CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_CFG_H      4
#define CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_CFG_H      4
#define CAN_43_LLCE_AR_RELEASE_REVISION_VERSION_CFG_H   0
#define CAN_43_LLCE_SW_MAJOR_VERSION_CFG_H              1
#define CAN_43_LLCE_SW_MINOR_VERSION_CFG_H              0
#define CAN_43_LLCE_SW_PATCH_VERSION_CFG_H              10

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types.h file are of the same Autosar version */
#if ((CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_CFG_H != STD_AR_RELEASE_MAJOR_VERSION) || \
(CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_CFG_H != STD_AR_RELEASE_MINOR_VERSION)    \
)
#error "AutoSar Version Numbers of Can_43_LLCE_Cfg.h and Std_Types.h are different"
#endif

#endif

/* Check if source file and CAN_43_LLCE_.h configuration header file are of the same vendor */
#if (CAN_43_LLCE_VENDOR_ID_VS_0_PBCFG_H != CAN_43_LLCE_VENDOR_ID_CFG_H)
#error "CAN_43_LLCE_VS_0_PBcfg.h and CAN_43_LLCE_Cfg.h have different vendor IDs"
#endif
/* Check if header file and CAN_43_LLCE_.h configuration header file are of the same Autosar version */
#if ((CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
(CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_CFG_H) || \
(CAN_43_LLCE_AR_RELEASE_REVISION_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_AR_RELEASE_REVISION_VERSION_CFG_H))
#error "AutoSar Version Numbers of CAN_43_LLCE_VS_0_PBcfg.h and CAN_43_LLCE_Cfg.h are different"
#endif
/* Check if header file and CAN_43_LLCE_.h configuration header file are of the same software version */
#if ((CAN_43_LLCE_SW_MAJOR_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_SW_MAJOR_VERSION_CFG_H) || \
(CAN_43_LLCE_SW_MINOR_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_SW_MINOR_VERSION_CFG_H) || \
(CAN_43_LLCE_SW_PATCH_VERSION_VS_0_PBCFG_H != CAN_43_LLCE_SW_PATCH_VERSION_CFG_H))
#error "Software Version Numbers of CAN_43_LLCE_VS_0_PBcfg.h and CAN_43_LLCE_Cfg.h are different"
#endif
/* Check if source file and CAN_43_LLCE_.h configuration header file are of the same vendor */
#if (CAN_43_LLCE_VENDOR_ID_VS_Headless_PBCFG_H != CAN_43_LLCE_VENDOR_ID_CFG_H)
#error "CAN_43_LLCE_VS_Headless_PBcfg.h and CAN_43_LLCE_Cfg.h have different vendor IDs"
#endif
/* Check if header file and CAN_43_LLCE_.h configuration header file are of the same Autosar version */
#if ((CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
(CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_AR_RELEASE_MINOR_VERSION_CFG_H) || \
(CAN_43_LLCE_AR_RELEASE_REVISION_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_AR_RELEASE_REVISION_VERSION_CFG_H))
#error "AutoSar Version Numbers of CAN_43_LLCE_VS_Headless_PBcfg.h and CAN_43_LLCE_Cfg.h are different"
#endif
/* Check if header file and CAN_43_LLCE_.h configuration header file are of the same software version */
#if ((CAN_43_LLCE_SW_MAJOR_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_SW_MAJOR_VERSION_CFG_H) || \
(CAN_43_LLCE_SW_MINOR_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_SW_MINOR_VERSION_CFG_H) || \
(CAN_43_LLCE_SW_PATCH_VERSION_VS_Headless_PBCFG_H != CAN_43_LLCE_SW_PATCH_VERSION_CFG_H))
#error "Software Version Numbers of CAN_43_LLCE_VS_Headless_PBcfg.h and CAN_43_LLCE_Cfg.h are different"
#endif


/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
/**
* @brief          Enable support for MAC generation and verification.
* @details        Enable support for MAC generation and verification.
*
*/


#define CAN_43_LLCE_HSE_SUPPORT_ENABLE    (STD_OFF)




#define RXLUT2_ENABLE  (STD_OFF) 




/**
* @brief          Implicit index value used by Full can hardware objects.
* @details        Implicit index value used by Full can hardware objects.
*
*/
#define CAN_43_LLCE_MAX_IDMASK    (Can_IdType)0x1FFFFFFFU





/*
* @brief          Symbolic names for CanObjectId
* @details        Symbolic names for CanObjectId maintained for compatibility with old testcases/applications 
*
*/
#define Rx_CCU_CANFD1  0U /* RECEIVE object of Can Controller ID = 0 */
#define Rx_CCU_CANFD2  1U /* RECEIVE object of Can Controller ID = 1 */
#define Rx_CCU_CANFD3  2U /* RECEIVE object of Can Controller ID = 2 */
#define Rx_CCU_CANFD4  3U /* RECEIVE object of Can Controller ID = 3 */
#define Rx_CCU_CANFD5  4U /* RECEIVE object of Can Controller ID = 4 */
#define Rx_CCU_CANFD6  5U /* RECEIVE object of Can Controller ID = 5 */
#define Rx_CCU_CANFD7  6U /* RECEIVE object of Can Controller ID = 6 */
#define Rx_CCU_CANFD8  7U /* RECEIVE object of Can Controller ID = 7 */
#define Rx_CCU_CANFD9  8U /* RECEIVE object of Can Controller ID = 8 */
#define Rx_CCU_CANFD10  9U /* RECEIVE object of Can Controller ID = 9 */
#define Rx_CCU_CANFD11  10U /* RECEIVE object of Can Controller ID = 10 */
#define Rx_CCU_CANFD12  11U /* RECEIVE object of Can Controller ID = 11 */
#define Rx_CCU_CANFD13  12U /* RECEIVE object of Can Controller ID = 12 */
#define Rx_CCU_CANFD14  13U /* RECEIVE object of Can Controller ID = 13 */
#define Tx_CCU_CANFD1  14U /* TRANSMIT object of Can Controller ID = 0 */
#define Tx_CCU_CANFD2  15U /* TRANSMIT object of Can Controller ID = 1 */
#define Tx_CCU_CANFD3  16U /* TRANSMIT object of Can Controller ID = 2 */
#define Tx_CCU_CANFD4  17U /* TRANSMIT object of Can Controller ID = 3 */
#define Tx_CCU_CANFD5  18U /* TRANSMIT object of Can Controller ID = 4 */
#define Tx_CCU_CANFD6  19U /* TRANSMIT object of Can Controller ID = 5 */
#define Tx_CCU_CANFD7  20U /* TRANSMIT object of Can Controller ID = 6 */
#define Tx_CCU_CANFD8  21U /* TRANSMIT object of Can Controller ID = 7 */
#define Tx_CCU_CANFD9  22U /* TRANSMIT object of Can Controller ID = 8 */
#define Tx_CCU_CANFD10  23U /* TRANSMIT object of Can Controller ID = 9 */
#define Tx_CCU_CANFD11  24U /* TRANSMIT object of Can Controller ID = 10 */
#define Tx_CCU_CANFD12  25U /* TRANSMIT object of Can Controller ID = 11 */
#define Tx_CCU_CANFD13  26U /* TRANSMIT object of Can Controller ID = 12 */
#define Tx_CCU_CANFD14  27U /* TRANSMIT object of Can Controller ID = 13 */

/*
* @brief          Lpdu callout name
* @details        (CAN357_Conf) CanLPduReceiveCalloutFunction - This parameter sets the name of the LPDU callout.
*
*/

#define CAN_43_LLCE_LPDU_NOTIFICATION_ENABLE  (STD_OFF)

#define CAN_43_LLCE_LPDU_CALLOUT_EXTENSION (STD_OFF)
/*
* @brief          Callout function name for timestamp of received frames
*
*/

#define CAN_43_LLCE_RX_TIMESTAMP  (STD_OFF)

/*
* @brief          Callout function name for timestamp of frame confirmations
*
*/

#define CAN_43_LLCE_ACK_TIMESTAMP  (STD_OFF)

/*
* @brief          Callback for frames received through customization filters
*
*/

#define CAN_43_LLCE_CUSTOM_RX_CALLBACK_USED  (STD_OFF)

/*
* @brief          Callback for each frame sent to LLCE firmware through Can_Write
*
*/

#define CAN_43_LLCE_CUSTOM_WRITE_CALLBACK_USED  (STD_OFF)

/*
* @brief          Callback for each TX Confirmation received from LLCE firmware.
*
*/

#define CAN_43_LLCE_CUSTOM_CONFIRMATION_CALLBACK  (STD_OFF)


/*
* @brief          Callout function name for reporting errors.
*
*/

#define CAN_43_LLCE_ERROR_NOTIF_ENABLE  (STD_OFF)

/*
* @brief          Extended identifiers.
* @details        Extended identifiers.
- (STD_ON)  - if at least one extended identifier is used.
- (STD_OFF) - if no extended identifiers are used at all
If no extended identifiers are used then the IDs and MASKs can be stored in uint16 rather than uint32.
*
*/

#define CAN_43_LLCE_SET_BAUDRATE_API (STD_ON)
#define CAN_LLCE_USE_HEADLESSMODE (STD_OFF)
/** Report controllers started without waiting to synchronise on bus */
#define CAN_43_LLCE_CONTROLLER_START_NOSYNC (STD_OFF)
/*
* @brief          Switches the Development Error Detection and Notification ON or OFF.
* @details        (CAN028) Call the Development Error Tracer when DET is switched on and the Can module detects an error.
(CAN082) The detection of development errors is configurable (ON / OFF) at pre- compile time.
The switch CanDevErrorDetect (see chapter 10) shall activate or deactivate the detection of all development errors.
*
*/
#define CAN_43_LLCE_DEV_ERROR_DETECT (STD_ON)
/*
* @brief          Switches between user mode and supervisor mode.
* @details        Switches between user mode and supervisor mode. Some peripherals, eg. sema42 require supervisor mode.
*
*/


/*
* @brief          Support for version info API
* @details        Switches the Can_43_LLCE_GetVersionInfo() API ON or OFF.
*
*/
#define CAN_43_LLCE_VERSION_INFO_API (STD_ON)
/*
* @brief          Instance # of the driver - used for Det_ReportError().
* @details        Instance # of the driver - used for Det_ReportError().
*
*/
#define CAN_43_LLCE_INSTANCE 0U

/*
* @brief          Enables the reporting to AUTOSAR modules. (e.g. CanIf, Det)
* @details        It's used for compatibility with DS, which can operate in non-ASR mode. 
*
*/
#ifndef AUTOSAR_COMPATIBLE_MODE
#define AUTOSAR_COMPATIBLE_MODE   (STD_ON)
#endif



/**
*   @brief      The definition used for guarding GetCoreID/Multicore
*/
#define CAN_43_LLCE_MULTICORE_ENABLED    (STD_ON)

/**
*   @brief      The definition represent for number of ECUC partition configured.
*/
#define CAN_43_LLCE_MAX_PARTITIONS    6U

/*
* @brief          The define in order to check the trigger transmit feature is enabled or disabled.
* @details        The define in order to check the trigger transmit feature is enabled or disabled.
*
*/

#define CAN_43_LLCE_TRIGGER_TRANSMIT_EN (STD_OFF)

/*
* @brief          The define in order to check the dummy hrh feature is enabled or disabled.
* @details        The define in order to check the dummy hrh transmit feature is enabled or disabled.
*
*/

#define CAN_43_LLCE_DUMMY_HRH_EN (STD_OFF)

/*123 Calculate the maximum number of HRH and HTH amd MBs from all config sets */

/*
* @brief          Maximum number of HRHs configured.
*
*/
#define CAN_43_LLCE_MAXHRH_CONFIGURED 14U

/*
* @brief          Maximum number of HTHs configured.
*
*/       
#define CAN_43_LLCE_MAXHTH_CONFIGURED 14U

/*
* @brief          Maximum number of HOHs configured.
*
*/       
#define CAN_43_LLCE_MAXHOH_CONFIGURED (CAN_43_LLCE_MAXHRH_CONFIGURED  + CAN_43_LLCE_MAXHTH_CONFIGURED  )

/*
* @brief          The index of the first HTH configured
*
*/  
#define CAN_43_LLCE_FIRST_HTH_CONFIGURED 14U

/*
* @brief          Maximum number of RXMBs configured.
*
*/  
#define CAN_43_LLCE_MAXRXMB_CONFIGURED 896U

/*
* @brief          Maximum number of TXMBs configured.
*
*/ 
#define CAN_43_LLCE_MAXTXMB_CONFIGURED 140U


/*
* @brief          Maximum No. of controllers Configured
* @details      Maximum No. of controllers Configured
*
*/
#define CAN_43_LLCE_MAXCTRL_CONFIGURED 14U

/**
* @brief          Hardware events detected by an interrupt or by polling
* @details        (CAN099) The hardware events may be detected by an interrupt or by polling status flags of the hardware objects.
*                 The configuration possibilities regarding polling is hardware dependent (i.e. which events can be polled, which events need to be polled), and not restricted by this standard.
*                 (CAN007)It shall be possible to configure the driver such that no interrupts at all are used (complete polling).
*
*/

/*
* @brief          This macro enables Can_43_LLCE_MainFunction_Write() if at least one controller is set to process Tx in Polling Mode.
* @details        This macro enables Can_43_LLCE_MainFunction_Write() if at least one controller is set to process Tx in Polling Mode.
*
*/
#define CAN_43_LLCE_TXPOLL_SUPPORTED (STD_OFF)

/*
* @brief          This macro enables Can_43_LLCE_MainFunction_Read() if at least one controller is set to process Rx in Polling Mode.
* @details        This macro enables Can_43_LLCE_MainFunction_Read() if at least one controller is set to process Rx in Polling Mode.
*
*/
#define CAN_43_LLCE_RXPOLL_SUPPORTED (STD_OFF)




/*
* @brief          This macro enables Can_43_LLCE_MainFunction_BusOff() if at least one controller is set to process BusOff in Polling Mode.
* @details        This macro enables Can_43_LLCE_MainFunction_BusOff() if at least one controller is set to process BusOff in Polling Mode.
*
*/
#define CAN_43_LLCE_BUSOFFPOLL_SUPPORTED (STD_ON)


/*
* @brief          This macro enables Can_43_LLCE_MainFunction_ErrorNotification() and is used for reporting errors configured in Polling Mode.
*
*/
#define CAN_43_LLCE_ERRORNOTIFPOLL_SUPPORTED (STD_OFF)


/**
* @brief          Maximum number of baudrate configured.
* @details        Maximum number of baudrate configured.
*                 Controller Baudrates configured are in kbps
*
*/
#define CAN_43_LLCE_MAX_BAUDRATE_FC_0_0    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_1_1    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_3_2    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_4_3    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_5_4    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_6_5    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_7_6    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_8_7    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_9_8    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_10_9    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_11_10    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_12_11    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_13_12    1U
#define CAN_43_LLCE_MAX_BAUDRATE_FC_15_13    1U

/* CAN FD INIT*/
#define CAN_43_LLCE_FD_MODE_ENABLE (STD_ON)

/*
* @brief          (CAN113_Conf) Specifies the maximum time for blocking function until a timeout is detected. Unit in loops.
* @details        (CAN281) The Can module shall use the free running timer provided by the system service for timeout detection in case the hardware does not react
*                  in the expected time (hardware malfunction) to prevent endless loops.
*
*/
/* Time out value in uS */
#define CAN_43_LLCE_TIMEOUT_DURATION    10000U

/* This will set the timer source for osif that will be used for timeout */
#define CAN_43_LLCE_SERVICE_TIMEOUT_TYPE    OSIF_COUNTER_SYSTEM



/*
* @brief          Precompile Support.
* @details        (CAN220)VARIANT-PRE-COMPILE: Only pre-compile configuration parameters
*
*/

#define CAN_43_LLCE_PRECOMPILE_SUPPORT (STD_OFF)



/*
* @brief          Periods for cyclic call of Main function
* @details        (ECUC_Can_00484) This parameter describes the period for cyclic call to Can_43_LLCE_MainFunction_Read or Can_43_LLCE_MainFunction_Write depending on the referring item. Unit is seconds. 
*                 Different poll-cycles will be configurable if more than one CanMainFunctionPeriod is configured. 
*                 In this case multiple Can_43_LLCE_MainFunction_Read() or Can_43_LLCE_MainFunction_Write() will be provided by the CAN Driver module..
*
*/

#define CAN_43_LLCE_MAINFUNCTION_MULTIPLE_READ  (STD_OFF)
#define CAN_43_LLCE_MAINFUNCTION_MULTIPLE_WRITE (STD_OFF)


/*
* @brief          Periods for cyclic call of Main function Mode
* @details        (CAN376_Conf) This parameter describes the period for cyclic call to Can_43_LLCE_MainFunction_Mode. Unit is seconds.
*
*/
#define CAN_43_LLCE_MAINFUNCTION_MODE_PERIOD (0.005F)

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define CAN_43_LLCE_CONFIG_EXT \
    CAN_43_LLCE_CONFIG_VS_0_PB \
    CAN_43_LLCE_CONFIG_VS_Headless_PB 

/*
* @brief          Support for Special MB Abort API
* @details        Enables an additional API which adds support for aborting a message transmission.
*/
#define CAN_43_LLCE_API_ENABLE_ABORT_MB (STD_OFF)

/*
* @brief          Support for single MB Abort  API
* @details        Extra information for AbortMB API which selects between single MB abort and aborting all the MBs corresponding to a specific HTH.
*/
#define CAN_43_LLCE_ABORT_ONLY_ONE_MB (STD_OFF)

/*
* @brief          Support for Manual BusOff recovery request API
* @details        Enables an additional API for requesting recovery from BusOff confinement state. 
*                 It is enabled when at least one controller configuration handles the recovery from bus-off manually.
*                 Not Autosar required.
*/
#define CAN_43_LLCE_API_MANUAL_BUSOFF_RECOVERY (STD_OFF)
/*
* @brief          Checks whether the BUSOFF_RECOVERY mode is ENABLED.
* @details        BUSOFF_RECOVERY is ENABLED when Manual or Auto Bus-off Recovery is set for any controller.
*                 Not Autosar required.
*/

#define CAN_43_LLCE_BUSOFF_RECOVERY_ENABLED     (STD_OFF)

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

#endif /* CAN_43_LLCE_CFG_H */

/** @} */
