/**
*   @file    Can_43_LLCE_Externals.h
*   @implements Can_Externals.h_Artifact
*   @version 1.0.10
*
*   @brief   AUTOSAR Can_43_LLCE - module interface.
*   @details API header for CAN driver.
*
*   @addtogroup CAN_LLCE
*   @{
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : LLCE
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 1.0.10
*   Build Version        : S32_RTD_1_0_10_D2505_ASR_REL_4_4_REV_0000_20250516
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/
#ifndef CAN_43_LLCE_EXTERNALS_H
#define CAN_43_LLCE_EXTERNALS_H

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define CAN_43_LLCE_EXTERNALS_VENDOR_ID_H                       43
#define CAN_43_LLCE_EXTERNALS_MODULE_ID_H                       80
#define CAN_43_LLCE_EXTERNALS_AR_RELEASE_MAJOR_VERSION_H        4
#define CAN_43_LLCE_EXTERNALS_AR_RELEASE_MINOR_VERSION_H        4
#define CAN_43_LLCE_EXTERNALS_AR_RELEASE_REVISION_VERSION_H     0
#define CAN_43_LLCE_EXTERNALS_SW_MAJOR_VERSION_H                1
#define CAN_43_LLCE_EXTERNALS_SW_MINOR_VERSION_H                0
#define CAN_43_LLCE_EXTERNALS_SW_PATCH_VERSION_H                10

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/
#define CAN_43_LLCE_START_SEC_CODE
#include "Can_43_LLCE_MemMap.h"

#define CAN_43_LLCE_STOP_SEC_CODE
#include "Can_43_LLCE_MemMap.h"
#ifdef __cplusplus
}
#endif
#endif /* CAN_43_LLCE_EXTERNALS_H */
/** @} */
