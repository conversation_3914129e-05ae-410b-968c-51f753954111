<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Fls</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Fls</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M50I0R0/Fls</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <POST-BUILD-VARIANT-USED>true</POST-BUILD-VARIANT-USED>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>AutosarExt</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsEnableUserModeSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsExternalSectorsConfigured</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsMCoreEnable</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsMCoreQJobSemaphoreChannelNo</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsQspiHangRecovery</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsQspiLockLUT</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/AutosarExt/FlsSynchronizeCache</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>92</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>5</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/VendorApiInfix</DEFINITION-REF>
                  <VALUE></VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>43</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>FlsConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsAcErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsAcErasePointer</DEFINITION-REF>
                  <VALUE>NULL_PTR</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsAcWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsAcWritePointer</DEFINITION-REF>
                  <VALUE>NULL_PTR</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsCallCycle</DEFINITION-REF>
                  <VALUE>0.2</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsDefaultMode</DEFINITION-REF>
                  <VALUE>MEMIF_MODE_SLOW</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsMaxReadFastMode</DEFINITION-REF>
                  <VALUE>1024</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsMaxReadNormalMode</DEFINITION-REF>
                  <VALUE>512</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsMaxWriteFastMode</DEFINITION-REF>
                  <VALUE>1024</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsMaxWriteNormalMode</DEFINITION-REF>
                  <VALUE>320</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsProtection</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>FlsExternalDr</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver</DEFINITION-REF>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsSpiReference</DEFINITION-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>ControllerCfg_SDR</SHORT-NAME>
                      <INDEX>0</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsClockOnDifferentialCknPadA</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsClockOnDifferentialCknPadB</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsDdrCentrerAlignReadA</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsDdrCentrerAlignReadB</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitByteSwapping</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitColumnAddressWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitDqsLatencyEnable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitReadMode</DEFINITION-REF>
                          <VALUE>QSPI_IP_DATA_RATE_SDR</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingDly</DEFINITION-REF>
                          <VALUE>QSPI_IP_SAMPLE_DELAY_SAME_DQS</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingEdge</DEFINITION-REF>
                          <VALUE>QSPI_IP_SAMPLE_PHASE_NON_INVERTED</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingModeA</DEFINITION-REF>
                          <VALUE>QSPI_IP_READ_MODE_LOOPBACK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingModeB</DEFINITION-REF>
                          <VALUE>QSPI_IP_READ_MODE_LOOPBACK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTcsh</DEFINITION-REF>
                          <VALUE>3</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTcss</DEFINITION-REF>
                          <VALUE>3</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTdh</DEFINITION-REF>
                          <VALUE>QSPI_IP_FLASH_DATA_ALIGN_REFCLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitWordAddressable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashA1Size</DEFINITION-REF>
                          <VALUE>33554432</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashA2Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashB1Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashB2Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFA2HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFA3HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFB2HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFB3HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DllCfgA</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraFreqEn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraReferenceCounter</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraResolution</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvDlyCoarse</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvDlyOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvFineOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllMode</DEFINITION-REF>
                              <VALUE>QSPI_IP_DLL_BYPASSED</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllTapSelect</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DllCfgB</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraFreqEn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvDlyCoarse</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvDlyOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvFineOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCrbReferenceCounter</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCrbResolution</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllMode</DEFINITION-REF>
                              <VALUE>QSPI_IP_DLL_BYPASSED</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllTapSelect</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>ControllerCfg_DDR_DQS_External</SHORT-NAME>
                      <INDEX>1</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsClockOnDifferentialCknPadA</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsClockOnDifferentialCknPadB</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsDdrCentrerAlignReadA</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsDdrCentrerAlignReadB</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitByteSwapping</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitColumnAddressWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitDqsLatencyEnable</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitReadMode</DEFINITION-REF>
                          <VALUE>QSPI_IP_DATA_RATE_DDR</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingDly</DEFINITION-REF>
                          <VALUE>QSPI_IP_SAMPLE_DELAY_SAME_DQS</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingEdge</DEFINITION-REF>
                          <VALUE>QSPI_IP_SAMPLE_PHASE_NON_INVERTED</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingModeA</DEFINITION-REF>
                          <VALUE>QSPI_IP_READ_MODE_EXTERNAL_DQS</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitSamplingModeB</DEFINITION-REF>
                          <VALUE>QSPI_IP_READ_MODE_EXTERNAL_DQS</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTcsh</DEFINITION-REF>
                          <VALUE>3</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTcss</DEFINITION-REF>
                          <VALUE>3</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitTdh</DEFINITION-REF>
                          <VALUE>QSPI_IP_FLASH_DATA_ALIGN_2X_REFCLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsHwUnitWordAddressable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashA1Size</DEFINITION-REF>
                          <VALUE>33554432</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashA2Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashB1Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsSerialFlashB2Size</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFA2HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFA3HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFB2HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/IdleSignalDriveIOFB3HighLvl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DllCfgA</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraFreqEn</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraReferenceCounter</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraResolution</DEFINITION-REF>
                              <VALUE>8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvDlyCoarse</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvDlyOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllCraSlvFineOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllMode</DEFINITION-REF>
                              <VALUE>QSPI_IP_DLL_AUTO_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgA/DllCfgADllTapSelect</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DllCfgB</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraFreqEn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvDlyCoarse</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvDlyOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCraSlvFineOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCrbReferenceCounter</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllCrbResolution</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllMode</DEFINITION-REF>
                              <VALUE>QSPI_IP_DLL_BYPASSED</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/DllCfgB/DllCfgBDllTapSelect</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>FlsAhbBuffer_3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferAllMasters</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferInstance</DEFINITION-REF>
                              <VALUE>AHB_BUFFER_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferMasterId</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/ControllerCfg/FlsAhbBuffer/FlsAhbBufferSize</DEFINITION-REF>
                              <VALUE>256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>FlsController_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsController</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsController/ControllerName</DEFINITION-REF>
                          <VALUE>FLS_QSPI_0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsController/FlsControllerCfgRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/ControllerCfg_SDR</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>FlsMem_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/AHBReadEnable</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/FlsMemName</DEFINITION-REF>
                          <VALUE>Macronix</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/FlsMemUseSfdp</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/MemAlignment</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/connectionType</DEFINITION-REF>
                          <VALUE>QSPI_IP_SIDE_A1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/FlsMemCfgRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/FlsMem/qspiInstance</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/FlsController_0</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>MemCfg_DOPI</SHORT-NAME>
                      <INDEX>0</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgPageSize</DEFINITION-REF>
                          <VALUE>256</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgSize</DEFINITION-REF>
                          <VALUE>33554432</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgReadLUT</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/Read_dopi</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgWriteLUT</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/Write_dopi</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/ctrlAutoCfgPtr</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/ControllerCfg_SDR</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>Erase_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>33</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>222</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>Erase_dopi_block</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>15</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>220</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>35</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>InitReset</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>13</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>102</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_STOP</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>153</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RDCR2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>113</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_READ</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>ReadId_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>159</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>96</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_6</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_DUMMY</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_7</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_READ</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>ReadSR</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>12</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>5</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_READ</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>ReadSR_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>5</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>250</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_6</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_DUMMY</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>20</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_7</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_READ</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>Read_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>238</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>17</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_3</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_DUMMY</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>20</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_4</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_READ_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>16</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>ResetEnable_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>6</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>102</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>153</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>Reset_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>153</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>102</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RuntimeReset</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>14</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>102</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_4</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_STOP</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>153</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_4</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>WRCR2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>10</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>114</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_WRITE</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>WriteEnable</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>11</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>6</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>WriteEnable_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>5</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>6</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>249</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>WriteSR_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>4</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>254</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_3</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_WRITE</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>Write_dopi</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsLUTIndex</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_0</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>18</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_CMD_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>237</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_2</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_ADDR_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>32</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>FlsInstructionOperandPair_3</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsInstrOperPairIndex</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTInstruction</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_INSTR_WRITE_DDR</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTOperand</DEFINITION-REF>
                                  <VALUE>16</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/FlsLUT/FlsInstructionOperandPair/FlsLUTPad</DEFINITION-REF>
                                  <VALUE>QSPI_IP_LUT_PADS_8</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>MemCfgEraseSettings</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase1Size</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase2Size</DEFINITION-REF>
                              <VALUE>12</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase3Size</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase4Size</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase1LUT</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/Erase_dopi_block</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgEraseSettings/MemCfgErase2LUT</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/Erase_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>MemCfgReadIdSettings</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgReadIdSettings</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgReadIdSettings/FlsQspiDeviceId</DEFINITION-REF>
                              <VALUE>0x39:81:C2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgReadIdSettings/MemCfgReadIdSize</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/MemCfgReadIdSettings/MemCfgReadIdLUT</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/ReadId_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>write_cr2_dopi</SHORT-NAME>
                          <INDEX>0</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/addr</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/opType</DEFINITION-REF>
                              <VALUE>QSPI_IP_OP_TYPE_RMW_REG</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/shift</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/size</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/value</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/width</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/command1Lut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/RDCR2</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/command2Lut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/WRCR2</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/weLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/WriteEnable</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>ext_dqs</SHORT-NAME>
                          <INDEX>1</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/addr</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/opType</DEFINITION-REF>
                              <VALUE>QSPI_IP_OP_TYPE_QSPI_CFG</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/shift</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/size</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/value</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/width</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initConfiguration/ctrlCfgPtr</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/ControllerCfg_DDR_DQS_External</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>initResetSettings</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initResetSettings</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initResetSettings/resetCmdCount</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/initResetSettings/resetCmdLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/InitReset</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>resetSettings</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/resetSettings</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/resetSettings/resetCmdCount</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/resetSettings/resetCmdLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/RuntimeReset</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>statusConfig</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/blockProtectionOffset</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/blockProtectionValue</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/blockProtectionWidth</DEFINITION-REF>
                              <VALUE>4</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/busyOffset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/busyValue</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/regSize</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/writeEnableOffset</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/statusRegInitReadLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/ReadSR</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/statusRegReadLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/ReadSR_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/statusRegWriteLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/WriteSR_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/writeEnableLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/WriteEnable_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/statusConfig/writeEnableSRLut</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/MemCfg_DOPI/WriteEnable_dopi</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>suspendSettings</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsExternalDriver/MemCfg/suspendSettings</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>FlsSectorList</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>FlsSector_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsHwCh</DEFINITION-REF>
                          <VALUE>FLS_CH_QSPI</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsNumberOfSectors</DEFINITION-REF>
                          <VALUE>1024</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsPageSize</DEFINITION-REF>
                          <VALUE>16</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsPageWriteAsynch</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsPhysicalSector</DEFINITION-REF>
                          <VALUE>FLS_EXT_SECTOR</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorEraseAsynch</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorHwAddress</DEFINITION-REF>
                          <VALUE>29360128</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorIndex</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorSize</DEFINITION-REF>
                          <VALUE>4096</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorStartaddress</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M50I0R0/Fls/FlsConfigSet/FlsSectorList/FlsSector/flashInstance</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Fls/Fls/FlsConfigSet/FlsExternalDr/FlsMem_0</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>FlsGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsAcLoadOnJobStart</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsBaseAddress</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsBlankCheckApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsCancelApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsCompareApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsDevErrorDetect</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsDriverIndex</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsEnableCheckCfgCrc</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsEnableDevAssert</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsEraseVerificationEnabled</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsGetJobResultApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsGetStatusApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsMCoreArbitrationTimeout</DEFINITION-REF>
                  <VALUE>32767</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsMCoreInitTimeout</DEFINITION-REF>
                  <VALUE>32767</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsMaxEraseBlankCheck</DEFINITION-REF>
                  <VALUE>256</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiAsyncEraseTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiAsyncWriteTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiCommandCompleteTimeout</DEFINITION-REF>
                  <VALUE>32767</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiDllLockTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiFlashInitTimeout</DEFINITION-REF>
                  <VALUE>32767</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiIpTimeoutOsifCounterType</DEFINITION-REF>
                  <VALUE>OSIF_COUNTER_DUMMY</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiResetTimeout</DEFINITION-REF>
                  <VALUE>327670</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiSoftwareResetDelay</DEFINITION-REF>
                  <VALUE>276</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiSyncEraseTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiSyncReadTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiSyncWriteTimeout</DEFINITION-REF>
                  <VALUE>2147483647</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiTxBufferResetDelay</DEFINITION-REF>
                  <VALUE>559</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsQspiWriteEnableRetries</DEFINITION-REF>
                  <VALUE>3</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsSetModeApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsTimeoutMethod</DEFINITION-REF>
                  <VALUE>OSIF_COUNTER_DUMMY</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsTimeoutSupervisionEnabled</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsTotalSize</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsUseInterrupts</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsVersionInfoApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsGeneral/FlsWriteVerificationEnabled</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>FlsPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsAcLocationErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsAcLocationWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsAcSizeErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsAcSizeWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsECCValue</DEFINITION-REF>
                  <VALUE>4294967295</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsEraseTime</DEFINITION-REF>
                  <VALUE>5.0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsErasedValue</DEFINITION-REF>
                  <VALUE>4294967295</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsExpectedHwId</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsSpecifiedEraseCycles</DEFINITION-REF>
                  <VALUE>100000</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Fls/FlsPublishedInformation/FlsWriteTime</DEFINITION-REF>
                  <VALUE>5.0E-4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
