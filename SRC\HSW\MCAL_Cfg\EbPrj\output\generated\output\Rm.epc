<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Rm</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Rm</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M50I0R0/Rm</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <POST-BUILD-VARIANT-USED>false</POST-BUILD-VARIANT-USED>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>255</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>5</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>43</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>RmConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MPU_M7_Configuration</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MpuDevErrorDetect</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>MPU_M7_ModuleConfig_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/DefaultMapEnable</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/MemManageInterruptEnable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/PhysicalCoreID</DEFINITION-REF>
                          <VALUE>CORTEX_M7_CORE0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RunInHFNMIEnable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_UNPRIV_NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>4294967295</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_STRONG_ORDER</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>4294967296</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>536870911</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>536870912</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_10</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RW_UNPRIV_RW</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>3759144959</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_STRONG_ORDER</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>10</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>1048576</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>3758096384</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>538968063</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_STRONG_ORDER</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>2097152</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>536870912</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>583024639</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>16384</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>583008256</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>604012543</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>4</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>32768</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>603979776</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_5</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>876609535</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>5</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>4194304</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>872415232</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_6</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>878706687</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_W_BACK_WR_ALLOCATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>6</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>2097152</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>876609536</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_7</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RWX_UNPRIV_RWX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>878706687</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_NORMAL_CACHEABLE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>1048576</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>877658112</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_8</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RW_UNPRIV_RW</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>1610612735</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_STRONG_ORDER</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>536870912</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>1073741824</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>RegionConfig_9</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/AccessRights</DEFINITION-REF>
                              <VALUE>MPU_M7_PRIV_RW_UNPRIV_RW</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/EndAddress</DEFINITION-REF>
                              <VALUE>1140850687</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/InnerCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/MemoryType</DEFINITION-REF>
                              <VALUE>MPU_M7_MEM_STRONG_ORDER</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/OuterCachePolicy</DEFINITION-REF>
                              <VALUE>MPU_M7_CACHE_POLICY_NO_CACHE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionNumber</DEFINITION-REF>
                              <VALUE>9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/RegionSize</DEFINITION-REF>
                              <VALUE>16777216</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/Shareable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/StartAddress</DEFINITION-REF>
                              <VALUE>1124073472</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/MPU_M7_Configuration/MPU_M7_ModuleConfig/RegionConfig/SubregionMask</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Mscm_Configuration</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Mscm_Configuration</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Mscm_Configuration/MscmDevErrorDetect</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Sema42_ModuleConfig</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Sema42_ModuleConfig</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Sema42_ModuleConfig/Sema42DevErrorDetect</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Sema42_ModuleConfig/Sema42ResetAllGates</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Xrdc_Configuration</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Xrdc_Configuration</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Xrdc_Configuration/XrdcCRLockBit</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Xrdc_Configuration/XrdcDevErrorDetect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Xrdc_Configuration/XrdcPIDRegisterLock</DEFINITION-REF>
                      <VALUE>XRDC_PID_UNLOCKED</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmConfigSet/Xrdc_Configuration/XrdcRegistersLock</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>RmGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmDevErrorDetect</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmEnableMscmSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmEnableMultiCore</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmEnableSema42Support</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmEnableUserModeSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmEnableXRDCSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/RmMpuM7Configurable</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Rm/RmGeneral/Rm_VersionInfoApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
