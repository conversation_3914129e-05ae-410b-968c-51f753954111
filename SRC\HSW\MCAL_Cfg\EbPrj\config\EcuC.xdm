<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="EcuC" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="EcuC" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/EcuC"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="EcucHardware" type="MAP">
                <d:ctr name="EcucHardware" type="IDENTIFIABLE">
                  <d:lst name="EcucCoreDefinition" type="MAP">
                    <d:ctr name="EcucCoreDefinition_A0" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                    <d:ctr name="EcucCoreDefinition_A1" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                    <d:ctr name="EcucCoreDefinition_A2" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                    <d:ctr name="EcucCoreDefinition_A3" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="3">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                    <d:ctr name="EcucCoreDefinition_M7_0" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="4">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                    <d:ctr name="EcucCoreDefinition_M7_1" type="IDENTIFIABLE">
                      <d:var name="EcucCoreId" type="INTEGER" value="5">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:lst name="EcucCoreHwRef"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
              <d:lst name="EcucPartitionCollection" type="MAP">
                <d:ctr name="EcucPartitionCollection" type="IDENTIFIABLE">
                  <d:lst name="EcucPartition" type="MAP">
                    <d:ctr name="EcucPartition_0" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                    <d:ctr name="EcucPartition_1" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                    <d:ctr name="EcucPartition_2" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                    <d:ctr name="EcucPartition_3" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                    <d:ctr name="EcucPartition_4" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                    <d:ctr name="EcucPartition_5" type="IDENTIFIABLE">
                      <d:var name="EcucPartitionBswModuleExecution" 
                             type="BOOLEAN" value="true"/>
                      <d:var name="EcucPartitionQmBswModuleExecution" 
                             type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="PartitionCanBeRestarted" type="BOOLEAN" 
                             value="true"/>
                      <d:lst 
                             name="EcucPartitionBswModuleDistinguishedPartition"/>
                      <d:lst name="EcucPartitionSoftwareComponentInstanceRef"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
              <d:lst name="EcucPduCollection" type="MAP"/>
              <d:ctr name="EcucSelectedLoadableVariant" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="false"/>
                <d:ref name="EcucSelectedLoadableRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="EcucPostBuildVariants" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="true"/>
                <d:ref name="EcucSelectedPostBuildVariantRef" type="REFERENCE" 
                       value="ASPath:/EB/PostBuildSelectable/VS_0"/>
                <d:lst name="EcucPostBuildVariantRef">
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EB/PostBuildSelectable/VS_0"/>
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EB/PostBuildSelectable/VS_Headless"/>
                </d:lst>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
