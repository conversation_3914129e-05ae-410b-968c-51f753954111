/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : IPV_QSPI
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

/**
 *   @file       Qspi_Ip_VS_Headless_PBcfg.c
 *
 *   @addtogroup IPV_QSPI QSPI IPV Driver
 *   @implements Qspi_Ip_PBcfg.c_Artifact
 *   @{
 */

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Qspi_Ip_VS_Headless_PBcfg.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define QSPI_IP_VS_HEADLESS_PBCFG_VENDOR_ID_C                      43
#define QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MAJOR_VERSION_C       4
#define QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MINOR_VERSION_C       4
#define QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_REVISION_VERSION_C    0
#define QSPI_IP_VS_HEADLESS_PBCFG_SW_MAJOR_VERSION_C               5
#define QSPI_IP_VS_HEADLESS_PBCFG_SW_MINOR_VERSION_C               0
#define QSPI_IP_VS_HEADLESS_PBCFG_SW_PATCH_VERSION_C               0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Qspi_Ip_VS_Headless_PBcfg.h header file are of the same vendor */
#if (QSPI_IP_VS_HEADLESS_PBCFG_VENDOR_ID_C != QSPI_IP_VS_HEADLESS_PBCFG_VENDOR_ID)
    #error "Qspi_Ip_VS_Headless_PBcfg.c and Qspi_Ip_VS_Headless_PBcfg.h have different vendor ids"
#endif
/* Check if current file and Qspi_Ip_VS_Headless_PBcfg.h header file are of the same Autosar version */
#if ((QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MINOR_VERSION_C    != QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_REVISION_VERSION_C != QSPI_IP_VS_HEADLESS_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
#error "AutoSar Version Numbers of Qspi_Ip_VS_Headless_PBcfg.c and Qspi_Ip_VS_Headless_PBcfg.h are different"
#endif
/* Check if current file and Qspi_Ip_VS_Headless_PBcfg.h header file are of the same software version */
#if ((QSPI_IP_VS_HEADLESS_PBCFG_SW_MAJOR_VERSION_C != QSPI_IP_VS_HEADLESS_PBCFG_SW_MAJOR_VERSION) || \
     (QSPI_IP_VS_HEADLESS_PBCFG_SW_MINOR_VERSION_C != QSPI_IP_VS_HEADLESS_PBCFG_SW_MINOR_VERSION) || \
     (QSPI_IP_VS_HEADLESS_PBCFG_SW_PATCH_VERSION_C != QSPI_IP_VS_HEADLESS_PBCFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Qspi_Ip_VS_Headless_PBcfg.c and Qspi_Ip_VS_Headless_PBcfg.h are different"
#endif

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

/* External QSPI flash parameters. */
/* paFlashConnectionCfg */
const Qspi_Ip_MemoryConnectionType FlsConfigSet_VS_Headless_paFlashConnectionCfg[1U] =
{
    {
        0U,  /* qspiInstance */
        QSPI_IP_SIDE_A1,  /* connectionType */
        1U  /* memAlignment */
    }
};

/* paQspiUnitCfg */
const Qspi_Ip_ControllerConfigType FlsConfigSet_VS_Headless_paQspiUnitCfg[2U] =
{
    /* ControllerCfg_SDR */
    {
        QSPI_IP_DATA_RATE_SDR,  /* dataRate */
        0U,  /* memSizeA1 */
        0U,  /* memSizeA2 */
        0U,  /* memSizeB1 */
        0U,  /* memSizeB2 */
        0U,  /* csHoldTime */
        0U,  /* csSetupTime */
        0U,  /* columnAddr */
        (boolean)FALSE,  /* wordAddresable */
        QSPI_IP_READ_MODE_LOOPBACK,  /* readModeA */
        QSPI_IP_READ_MODE_LOOPBACK,  /* readModeB */
        QSPI_IP_SAMPLE_DELAY_SAME_DQS,  /* sampleDelay */
        QSPI_IP_SAMPLE_PHASE_NON_INVERTED,  /* samplePhase */
        {
            /* dllSettingsA */
            QSPI_IP_DLL_BYPASSED,  /* dllMode */
            (boolean)FALSE,  /* freqEnable */
            0U,  /* referenceCounter */
            0U,  /* resolution */
            0U,  /* coarseDelay */
            0U,  /* fineDelay */
            0U   /* tapSelect */
        },
        {
            /* dllSettingsB */
            QSPI_IP_DLL_BYPASSED,  /* dllMode */
            (boolean)FALSE,  /* freqEnable */
            1U,  /* referenceCounter */
            2U,  /* resolution */
            0U,  /* coarseDelay */
            0U,  /* fineDelay */
            0U   /* tapSelect */
        },
        (boolean)FALSE,  /* centerAlignedStrobeA */
        (boolean)FALSE,  /* centerAlignedStrobeB */
        (boolean)FALSE,  /* differentialClockA */
        (boolean)FALSE,  /* differentialClockB */
        (boolean)FALSE,  /* dqsLatency */
        QSPI_IP_FLASH_DATA_ALIGN_REFCLK,  /* dataAlign */
        1U,  /* io2IdleValueA */
        1U,  /* io3IdleValueA */
        1U,  /* io2IdleValueB */
        1U,  /* io3IdleValueB */
        (boolean)FALSE,  /* byteSwap */
        {
            /* AHBConfig */
            {
                /* masters */
                0U,  /* buffer 0 master ID */
                1U,  /* buffer 1 master ID */
                2U,  /* buffer 2 master ID */
                3U   /* buffer 3 master ID */
            },
            {
                /* sizes */
                0U,  /* buffer 0 size */
                0U,  /* buffer 1 size */
                0U,  /* buffer 2 size */
                1024U   /* buffer 3 size */
            },
            (boolean)TRUE  /* allMasters */
        }
    },
    /* ControllerCfg_DDR_DQS_External */
    {
        QSPI_IP_DATA_RATE_SDR,  /* dataRate */
        0U,  /* memSizeA1 */
        0U,  /* memSizeA2 */
        0U,  /* memSizeB1 */
        0U,  /* memSizeB2 */
        3U,  /* csHoldTime */
        3U,  /* csSetupTime */
        0U,  /* columnAddr */
        (boolean)FALSE,  /* wordAddresable */
        QSPI_IP_READ_MODE_LOOPBACK,  /* readModeA */
        QSPI_IP_READ_MODE_LOOPBACK,  /* readModeB */
        QSPI_IP_SAMPLE_DELAY_SAME_DQS,  /* sampleDelay */
        QSPI_IP_SAMPLE_PHASE_NON_INVERTED,  /* samplePhase */
        {
            /* dllSettingsA */
            QSPI_IP_DLL_BYPASSED,  /* dllMode */
            (boolean)FALSE,  /* freqEnable */
            1U,  /* referenceCounter */
            2U,  /* resolution */
            0U,  /* coarseDelay */
            0U,  /* fineDelay */
            0U   /* tapSelect */
        },
        {
            /* dllSettingsB */
            QSPI_IP_DLL_BYPASSED,  /* dllMode */
            (boolean)FALSE,  /* freqEnable */
            1U,  /* referenceCounter */
            2U,  /* resolution */
            0U,  /* coarseDelay */
            0U,  /* fineDelay */
            0U   /* tapSelect */
        },
        (boolean)FALSE,  /* centerAlignedStrobeA */
        (boolean)FALSE,  /* centerAlignedStrobeB */
        (boolean)FALSE,  /* differentialClockA */
        (boolean)FALSE,  /* differentialClockB */
        (boolean)FALSE,  /* dqsLatency */
        QSPI_IP_FLASH_DATA_ALIGN_REFCLK,  /* dataAlign */
        1U,  /* io2IdleValueA */
        1U,  /* io3IdleValueA */
        1U,  /* io2IdleValueB */
        1U,  /* io3IdleValueB */
        (boolean)FALSE,  /* byteSwap */
        {
            /* AHBConfig */
            {
                /* masters */
                0U,  /* buffer 0 master ID */
                1U,  /* buffer 1 master ID */
                2U,  /* buffer 2 master ID */
                3U   /* buffer 3 master ID */
            },
            {
                /* sizes */
                0U,  /* buffer 0 size */
                0U,  /* buffer 1 size */
                0U,  /* buffer 2 size */
                1024U   /* buffer 3 size */
            },
            (boolean)TRUE  /* allMasters */
        }
    }
};


/* paInitOperations */
static Qspi_Ip_InitOperationType MemCfg_DOPI_VS_Headless_paInitOperations_0[2U] =
{
    {
        QSPI_IP_OP_TYPE_CMD,  /* opType */
        QSPI_IP_LUT_INVALID,  /* command1Lut */
        QSPI_IP_LUT_INVALID,  /* command2Lut */
        QSPI_IP_LUT_INVALID,  /* weLut */
        0U,  /* addr */
        1U,  /* size */
        0U,  /* shift */
        0U,  /* width */
        1U,  /* value */
        NULL_PTR  /* ctrlCfgPtr */
    },
    {
        QSPI_IP_OP_TYPE_CMD,  /* opType */
        QSPI_IP_LUT_INVALID,  /* command1Lut */
        QSPI_IP_LUT_INVALID,  /* command2Lut */
        QSPI_IP_LUT_INVALID,  /* weLut */
        0U,  /* addr */
        1U,  /* size */
        0U,  /* shift */
        0U,  /* width */
        1U,  /* value */
        NULL_PTR  /* ctrlCfgPtr */
    }
};


/* paLutOperations */
static Qspi_Ip_InstrOpType MemCfg_DOPI_VS_Headless_paLutOperations_0[66U] =
{
    /* 0: Read_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 6: Write_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 11: Erase_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 15: ReadSR_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 21: WriteSR_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 26: WriteEnable_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 29: ResetEnable_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 32: Reset_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 35: ReadId_dopi */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 41: RDCR2 */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 45: WRCR2 */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 49: WriteEnable */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 51: ReadSR */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 54: InitReset */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 58: RuntimeReset */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP),
    /* 62: Erase_dopi_block */
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)((Qspi_Ip_InstrOpType)QSPI_IP_LUT_INSTR_CMD | (Qspi_Ip_InstrOpType)QSPI_IP_LUT_PADS_1 | (Qspi_Ip_InstrOpType)0U),
    (Qspi_Ip_InstrOpType)(QSPI_IP_LUT_INSTR_STOP)
};


/* paFlashCfg */
const Qspi_Ip_MemoryConfigType FlsConfigSet_VS_Headless_paFlashCfg[1U] =
{
    {
        /* MemCfg_DOPI */
        QSPI_IP_SERIAL_FLASH,  /* memType */
        NULL_PTR,  /* hfConfig */
        33554432U,  /* memSize */
        256U,  /* pageSize */
        0U,  /* readLut */
        6U,  /* writeLut */
        QSPI_IP_LUT_INVALID,  /* read0xxLut */
        QSPI_IP_LUT_INVALID,  /* read0xxLutAHB */
        /* readIdSettings */
        {
            35U,  /* readIdLut */
            3U,  /* readIdSize */
            /* readIdExpected */
            {
                0xC2U,  /* byte 1 */
                0x81U,  /* byte 2 */
                0x39U,  /* byte 3 */
                0xFFU   /* byte 4 */
            }
        },
        {
            /* eraseSettings */
            {
                {
                    /* eraseTypes[0] */
                    62U,  /* eraseLut */
                    16U  /* size */
                },
                {
                    /* eraseTypes[1] */
                    11U,  /* eraseLut */
                    12U  /* size */
                },
                {
                    /* eraseTypes[2] */
                    QSPI_IP_LUT_INVALID,  /* eraseLut */
                    1U  /* size */
                },
                {
                    /* eraseTypes[3] */
                    QSPI_IP_LUT_INVALID,  /* eraseLut */
                    1U  /* size */
                }
            },
            QSPI_IP_LUT_INVALID  /* chipEraseLut */
        },
        {
            /* statusConfig */
            51U,  /* statusRegInitReadLut */
            15U,  /* statusRegReadLut */
            21U,  /* statusRegWriteLut */
            26U,  /* writeEnableSRLut */
            26U,  /* writeEnableLut */
            1U,  /* regSize */
            0U,  /* busyOffset */
            1U,  /* busyValue */
            1U,  /* writeEnableOffset */
            2U,  /* blockProtectionOffset */
            4U,  /* blockProtectionWidth */
            0U   /* blockProtectionValue */
        },
        {
            /* suspendSettings */
            QSPI_IP_LUT_INVALID,  /* eraseSuspendLut */
            QSPI_IP_LUT_INVALID,  /* eraseResumeLut */
            QSPI_IP_LUT_INVALID,  /* programSuspendLut */
            QSPI_IP_LUT_INVALID   /* programResumeLut */
        },
        {
            /* resetSettings */
            58U,  /* resetCmdLut */
            2U  /* resetCmdCount */
        },
        {
            /* initResetSettings */
            54U,  /* resetCmdLut */
            2U  /* resetCmdCount */
        },
        {
            /* initConfiguration */
            2U,  /* opCount */
            (Qspi_Ip_InitOperationType *)MemCfg_DOPI_VS_Headless_paInitOperations_0  /* operations */
        },
        {
            /* lutSequences */
            66U,  /* opCount */
            MemCfg_DOPI_VS_Headless_paLutOperations_0  /* lutOps */
        },
        NULL_PTR,  /* initCallout */
        NULL_PTR,  /* resetCallout */
        NULL_PTR,  /* errorCheckCallout */
        NULL_PTR,  /* eccCheckCallout */
        &(FlsConfigSet_VS_Headless_paQspiUnitCfg[0U])  /* ctrlAutoCfgPtr */
    }
};

#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


#ifdef __cplusplus
}
#endif

/** @}*/

