#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import shutil
import re
import json
# from struct import *
import binascii

class combin_bin():
    def __init__(self,config_file_path):
        self.config_file = config_file_path
        self.json_file_version = ""
        self.output_path = ""
        self.output_name = ""
        self.padding = ""
        self.combin_order = []
        self.combin_list = {}
        
    def combin(self):
        print("starting combin...")
        ret = self.analy_json(self.config_file)
        if ret == False:
            return False
        bin_file = bytes()
        for item in self.combin_order:
            if item in self.combin_list:
                print("\r\nanaly items:%s..." %(item))
                bin_file = self.pad_bytes_from_cfg_dict(item,bin_file,self.combin_list[item],self.padding)
                if not bin_file:
                    bin_file = bytes()
                    print("combin fail...!")
                    break
            else:
                print("%s is not in combin_list, pass" %(item))
        if len(bin_file) > 0:
            save_file = os.path.join(self.output_path, self.output_name)
            with open(save_file,"wb") as f:
                f.write(bin_file) 
            print("combin success...!")
                
                
        
    def pad_bytes_from_cfg_dict(self,items,src_bin,cfg_dict,padding):
        bin = src_bin
        if not isinstance(cfg_dict, dict):
            print("%s items value not a dict" %(items))
            return None
        if "type" not in cfg_dict:
            print("miss \"type\" items in %s " %(items))
            return None
        item_type = cfg_dict["type"]
        if "offset" not in cfg_dict:
            print("miss \"offset\" items in %s " %(items))
            return None
        offset = self.get_u32_hex_str(cfg_dict["offset"])
        if not offset:
            print("\"offset\" items in %s is not hex string" %(items))
            return None
        offset_addr = int(offset,16)
        print("items %s offset addr :%x"%(items,offset_addr))
        last_bin_len = len(bin)
        if last_bin_len > offset_addr:
            print("%s is overlap with last bin ,last bin len %x,items now offset addr %x"%(items,last_bin_len,offset_addr))
            return None
        if last_bin_len < offset_addr:
            padding_data = bytes([padding for i in range(offset_addr - last_bin_len)])
            bin += padding_data
            print("pad value %x length %d,before items %s to be added"%(padding,offset_addr - last_bin_len,items))
        
        print("items %s type:\"%s\""%(items,cfg_dict["type"]))
        if item_type == "padding_bytes":
            if "padding" not in cfg_dict:
                print("miss \"padding\" items in %s " %(items))
                return None
            padding_val = cfg_dict["padding"]
            
            if not isinstance(padding_val, int):
                print("value of items \"padding\" in %s format wrong" %(items))
                return None
            if padding_val > 255:
                print("value of items \"padding\" in %s mast be range of 0~255" %(items))
                return None
            if "length" not in cfg_dict:
                print("miss \"length\" items in %s " %(items))
                return None
            length = cfg_dict["length"]
            if not isinstance(length, int):
                print("value of items \"length\" in %s format wrong" %(items))
                return None
            
            print("padding len:%d,padding value:%d,into bin\r\n"%(length,padding_val))
            data = bytes([padding_val for i in range(length)])
            bin += data
        elif item_type == "file":
            align = cfg_dict["align"]
            if not isinstance(align, int):
                print("value of items \"align\" in %s format wrong" %(items))
                return None
            print("align len:%d\r\n"%(align))
            if "path" not in cfg_dict:
                print("miss \"path\" items in %s " %(items))
                return None
            path = cfg_dict["path"]
            if not os.path.isfile(path):
                print("value:%s of items \"path\" in %s is not a file" %(path,items))
                return None
  
            file = bytes()
            with open(path,"rb") as f:
                file = f.read()
            print("padding file into bin ,file size %d,file path:%s"%(len(file),path))
            bin = self.padding_bytes(bin,align,padding)
            bin += file
        elif item_type == "hexstr":
            if "endian" not in cfg_dict:
                print("miss \"endian\" items in %s " %(items))
                return None
            endian = cfg_dict["endian"]
            if not isinstance(endian, str):
                print("value of items \"endian\" in %s format wrong" %(items))
                return None
            if endian != "big" and endian != "little":
                print("value of items \"endian\" in %s not big and little" %(items))
                return None
            if "value" not in cfg_dict:
                print("miss \"value\" items in %s " %(items))
                return None
            value = cfg_dict["value"]
            if not isinstance(value, str):
                print("value of items \"value\" in %s format wrong" %(items))
                return None
            data = self.hexstr_to_bytes(value,endian)
            print("padding hextring into bin ,src string %s"%(value))
            print("convert data:")
            print([hex(i)[2:] for i in data])
            bin += data
        elif item_type == "word_str":
            if "endian" not in cfg_dict:
                print("miss \"endian\" items in %s " %(items))
                return None
            endian = cfg_dict["endian"]
            if not isinstance(endian, str):
                print("value of items \"endian\" in %s format wrong" %(items))
                return None
            if endian != "big" and endian != "little":
                print("value of items \"endian\" in %s not big and little" %(items))
                return None
            if "value" not in cfg_dict:
                print("miss \"value\" items in %s " %(items))
                return None
            value = cfg_dict["value"]
            if not isinstance(value, str):
                print("value of items \"value\" in %s format wrong" %(items))
                return None
            word_str = self.get_u32_hex_str(value)
            if not word_str:
                print("value of items \"value\" in %s not a hex string" %(items))
                return None
            word_bytes = bytes.fromhex(word_str)
            if endian == "little":
                byte_ls = [i for i in word_bytes]
                byte_ls = byte_ls[::-1]
                word_bytes = bytes(byte_ls)
            print("padding hex word string into bin ,src string %s"%(word_str))
            print("convert data:")
            print([hex(i)[2:] for i in word_bytes])
            bin += word_bytes
        elif item_type == "file_size":
            if "endian" not in cfg_dict:
                print("miss \"endian\" items in %s " %(items))
                return None
            endian = cfg_dict["endian"]
            if not isinstance(endian, str):
                print("value of items \"endian\" in %s format wrong" %(items))
                return None
            if "path" not in cfg_dict:
                print("miss \"path\" items in %s " %(items))
                return None
            align = cfg_dict["align"]
            if not isinstance(align, int):
                print("value of items \"align\" in %s format wrong" %(items))
                return None
            print("align len:%d\r\n"%(align))
            path = cfg_dict["path"]
            if not os.path.isfile(path):
                print("value:%s of items \"path\" in %s is not a file" %(path,items))
                return None
            
            file_size = os.path.getsize(path)
            file_size = (file_size + align - 1) // align * align
            file_size_str = self.get_u32_hex_str(file_size)
            word_bytes = bytes.fromhex(file_size_str)
            if endian == "little":
                byte_ls = [i for i in word_bytes]
                byte_ls = byte_ls[::-1]
                word_bytes = bytes(byte_ls)
            bin += word_bytes
        else:
            print("unsupport item type %s !")
            return None
        
        return bin
    def padding_bytes(self,bs,align,padding_val):
        if not isinstance(bs,bytes):
            print("input wrong padding instance")
            return None
        pad_len = align - (len(bs) % align)
        if pad_len < align:
            pad_bytes = bs + bytes([padding_val for i in range(pad_len)])
        else:#nothing to pad
            pad_bytes = bs
        return pad_bytes
    def adding_bytes(self,bs,align,adding_val):
        if not isinstance(bs,bytes):
            print("input wrong adding instance")
            return None
        pad_len = align - (len(bs) % align)
        if pad_len < align:
            pad_bytes = bytes([adding_val for i in range(pad_len)]) + bs  
        else:#nothing to pad
            pad_bytes = bs
        return pad_bytes
    def hexstr_to_bytes(self,hexstr,endian):
        if  not isinstance(hexstr, str):
            print("hexstr_to_bytes hexstr input not string format")
            return None
        
        value_str = hexstr.replace(" ","").strip().replace("_","").strip()
        pattern = r'(?!0[xX])[0-9A-Fa-f]+'
        match = re.search(pattern,value_str)
        if not match:
            print("hexstr_to_bytes hex string not match in %s"%(value_str))
            return None
        value_str = match.group(0)
        byte_data = bytes.fromhex(value_str)
        print(byte_data)
        if endian == "little":
            byte_ls = [i for i in byte_data]
            byte_ls = byte_ls[::-1]
            print(byte_ls)
            byte_data = bytes(byte_ls)
        
        return byte_data
    def get_u32_hex_str(self,input_para,is_align=True):
        u32_str_align_pack = ["00000000","0000000","000000","00000","0000","000","00","0",""]
        hex_str = ""
        if isinstance(input_para,str):
            value_str = input_para.replace(" ","").strip().replace("_","").strip()
            pattern = r'(?!0[xX])[0-9A-Fa-f]{1,8}'
            match = re.search(pattern,value_str)
            if not match:
                print("hex string not match in %s"%(value_str))
                return None
            value_str = match.group(0)
            if len(value_str) > 8:
                value_str = value_str[:8]
            elif is_align == True:
                value_str = u32_str_align_pack[len(value_str)] + value_str
            hex_str = value_str
        elif isinstance(input_para,int):
            value_str = str(hex(input_para))[2:]
            if len(value_str) > 8:
                value_str = value_str[:8]
            elif is_align == True:
                value_str = u32_str_align_pack[len(value_str)] + value_str
            hex_str = value_str
        else:
            print("get_u32_hex_str input instance not surpport")
            print(type(input_para))
            return None
        return hex_str
        
        
            
    def analy_json(self,json_path):
        print("analy config file...")
        if not os.path.isfile(json_path):
            print("config file path error:%s",json_path)
            return False
        json_dict = {}
        with open(json_path, 'r') as f:
            json_dict = json.load(f)
        if not isinstance(json_dict,dict):
            print(" input wrong json config file")
            return False
        
        #json_file_version
        if "json_file_version" not in json_dict:
            print("config file wrong:miss \"json_file_version\" item")
            return False
        self.json_file_version = json_dict["json_file_version"]
        print("config file version:%s" %(self.json_file_version))
        # combin_order
        if "combin_order" not in json_dict:
            print("config file wrong:miss \"combin_order\" item")
            return False
        self.combin_order = json_dict["combin_order"]
        if not isinstance(self.combin_order, list):
            print("config file wrong:\"combin_order\" is not list instance")
            return False
        
        # output_path
        if "output_path" not in json_dict:
            print("config file wrong:miss \"output_path\" item")
            return False
        self.output_path = json_dict["output_path"]
        
        if not os.path.isdir(self.output_path):
            print("config file wrong:\"output_path\" is not valid path")
            return False
        print("output path:%s" %(self.output_path))
        # combin_list
        if "combin_list" not in json_dict:
            print("config file wrong:miss \"combin_list\" item")
            return False
        self.combin_list = json_dict["combin_list"]
        if not isinstance(self.combin_list, dict):
            print("config file wrong:\"combin_list\" is not dict instance")
            return False
        
        # output_name
        if "output_name" not in json_dict:
            print("config file wrong:miss \"output_name\" item")
            return False
        self.output_name = json_dict["output_name"]
        if not isinstance(self.output_name, str):
            print("config file wrong:\"output_name\" is not string instance")
            return False
        print("output name:%s" %(self.output_name))
        
        
        # padding
        if "padding" not in json_dict:
            print("config file wrong:miss \"padding\" item")
            return False
        self.padding = json_dict["padding"]
        if not isinstance(self.padding, int):
            print("config file wrong:\"padding\" is not int instance")
            return False
        if self.padding > 255:
            print("config file wrong:\"padding\" value mast be range of 0~255")
            return False
        print("output name:%s" %(self.output_name))
        return True
        
if __name__ == '__main__':
    
    if len(sys.argv) < 2:
        print("please input config path as cmd arguments")
        sys.exit()
    in_path = sys.argv[1]    
    combin_instance = combin_bin(in_path)
    combin_instance.combin()
 

    '''
    #bytes 转string
    convert_byte2hex_align2 = lambda b: ('0'+ hex(b)[2:].upper()) if len(hex(b)[2:]) == 1  else hex(b)[2:].upper()
    b_str = ''.join([convert_byte2hex_align2(b) for b in databytes])
    print(b_str)
    '''
    
    # a = binascii.unhexlify("ff7f584d")
    # print(len(a))
    # print(type(a))
    # sum = 0
    # for b in a:
    #     sum += b
    # print(sum&0xff)