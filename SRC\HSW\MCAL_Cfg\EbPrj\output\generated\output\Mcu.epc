<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Mcu</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Mcu</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M50I0R0/Mcu</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <POST-BUILD-VARIANT-USED>true</POST-BUILD-VARIANT-USED>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>101</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>5</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/VendorApiInfix</DEFINITION-REF>
                  <VALUE></VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>43</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuCoreControlConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuCoreControlConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuCoreControlConfiguration/McuCoreBootAddressControl</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuDebugConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuDisableCmuApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuDisableDemReportErrorStatus</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuEmiosConfigureGprenApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuGetClockFrequencyApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuGetMidrStructureApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuGetPowerDomainApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuGetPowerModeStateApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuGetSystemStateApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuSscmGetMemConfigApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuSscmGetStatusApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuDebugConfiguration/McuSscmGetUoptApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuGeneralConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/A53CoreFlavour</DEFINITION-REF>
                  <VALUE>f1300MHz</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuAlternateResetIsrUsed</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuCalloutBeforePerformReset</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuCmuErrorIsrUsed</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuDevErrorDetect</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuDisablePmcInit</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuDisableRamWaitStatesConfig</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuDisableRgmInit</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuEnableUserModeSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuEnterLowPowerMode</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuGetRamStateApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuHardwareVersion</DEFINITION-REF>
                  <VALUE>Rev2</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuInitClock</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuNoPll</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuPerformResetApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuPerformResetCallout</DEFINITION-REF>
                  <VALUE>NULL_PTR</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuScmiPlatformSupport</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuTimeout</DEFINITION-REF>
                  <VALUE>50000</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuTimeoutMethod</DEFINITION-REF>
                  <VALUE>OSIF_COUNTER_DUMMY</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuVersionInfoApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>McuControlledClocksConfiguration</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuDfs0UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuDfs1UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuFxoscUnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuPll0UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuPll1UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuPll2UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuGeneralConfiguration/McuControlledClocksConfiguration/McuPll3UnderMcuControl</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuModuleConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSrcFailureNotification</DEFINITION-REF>
                  <VALUE>DISABLED</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuCrystalFrequencyHz</DEFINITION-REF>
                  <VALUE>4.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_FTM_0_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>2.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_FTM_1_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>2.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_0_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>5.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_0_EXT_RX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_0_EXT_TX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_1_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>5.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_1_EXT_RX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_1_EXT_TX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_GMAC_EXT_TS_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.0E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_0_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>5.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_0_EXT_RX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_0_EXT_TX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_1_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>5.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_1_EXT_RX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_1_EXT_TX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_2_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>5.0E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_2_EXT_RX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_PFE_MAC_2_EXT_TX_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuExternalPAD_RTC_EXT_REF_CLK_FrequencyHz</DEFINITION-REF>
                  <VALUE>4.8E7</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_LANE_0_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_LANE_0_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_LANE_1_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_LANE_1_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_XPCS_0_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_XPCS_0_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_XPCS_1_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_0_XPCS_1_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_LANE_0_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_LANE_0_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_LANE_1_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_LANE_1_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_XPCS_0_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_XPCS_0_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_XPCS_1_CDR_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuInternalPAD_SERDES_1_XPCS_1_TX_FrequencyHz</DEFINITION-REF>
                  <VALUE>1.25E8</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuNumberOfMcuModes</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuRamSectors</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>McuClockSettingConfig_0</SHORT-NAME>
                  <INDEX>0</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockSettingId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCgm0SettingConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuPCSStepDuration</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuPCSSwitchDuration</DEFINITION-REF>
                          <VALUE>48</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClkMux0Div0_Divisor</DEFINITION-REF>
                              <VALUE>15</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClkMux0Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClkMux0Div1_Divisor</DEFINITION-REF>
                              <VALUE>5</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClkMux0Div1_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClkMux0_Source</DEFINITION-REF>
                              <VALUE>CORE_PLL_DFS1_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClockMux0Divider0_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClockMux0Divider1_Frequency</DEFINITION-REF>
                              <VALUE>1.3333333333333333E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClockMux0_Frequency</DEFINITION-REF>
                              <VALUE>8.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1/McuClkMux1Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1/McuClkMux1Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1/McuClkMux1_Source</DEFINITION-REF>
                              <VALUE>FXOSC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1/McuClockMux1Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux1/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux10</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10/McuClkMux10Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10/McuClkMux10Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10/McuClkMux10_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI5_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10/McuClockMux10Divider0_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux10/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux11</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux11</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux11/McuClkMux11_Source</DEFINITION-REF>
                              <VALUE>GMAC_0_EXT_RX_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux11/McuClockMux11_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux11/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux12</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12/McuClkMux12Div0_Divisor</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12/McuClkMux12Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12/McuClkMux12_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_DFS1_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12/McuClockMux12Divider0_Frequency</DEFINITION-REF>
                              <VALUE>4.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux12/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux14</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14/McuClkMux14Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14/McuClkMux14Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14/McuClkMux14_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14/McuClockMux14Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux14/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux15</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClkMux15Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClkMux15Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClkMux15_Source</DEFINITION-REF>
                              <VALUE>GMAC_0_EXT_REF_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClockMux15Divider0_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClockMux15_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux15/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux16</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux16</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux16/McuClkMux16_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI7_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux16/McuClockMux16_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux16/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2/McuClkMux2Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2/McuClkMux2Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2/McuClkMux2_Source</DEFINITION-REF>
                              <VALUE>FXOSC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2/McuClockMux2Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux2/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3/McuClkMux3Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3/McuClkMux3Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3/McuClkMux3_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI1_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3/McuClockMux3Divider0_Frequency</DEFINITION-REF>
                              <VALUE>6.25E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux3/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4/McuClkMux4Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4/McuClkMux4Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4/McuClkMux4_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4/McuClockMux4Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux4/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux5</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5/McuClkMux5Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5/McuClkMux5Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5/McuClkMux5_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5/McuClockMux5Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux5/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux6</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6/McuClkMux6Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6/McuClkMux6Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6/McuClkMux6_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6/McuClockMux6Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux6/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux7</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux7</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux7/McuClkMux7_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI2_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux7/McuClockMux7_Frequency</DEFINITION-REF>
                              <VALUE>4.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux7/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux8</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux8</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux8/McuClkMux8_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux8/McuClockMux8_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux8/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0ClockMux9</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9/McuClkMux9Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9/McuClkMux9Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9/McuClkMux9_Source</DEFINITION-REF>
                              <VALUE>GMAC_EXT_TS_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9/McuClockMux9Divider0_Frequency</DEFINITION-REF>
                              <VALUE>1.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0ClockMux9/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm0PcsConfig_0</SHORT-NAME>
                          <INDEX>0</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0PcsConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0PcsConfig/McuClockPcfsUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0PcsConfig/McuPCS_MaxAllowableDynamicIDD</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0PcsConfig/McuPCS_Name</DEFINITION-REF>
                              <VALUE>PCFS_12</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm0SettingConfig/McuCgm0PcsConfig/McuPCS_SourceFrequency</DEFINITION-REF>
                              <VALUE>8.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCgm1SettingConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuPCSStepDuration</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuPCSSwitchDuration</DEFINITION-REF>
                          <VALUE>48</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm1ClockMux0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1ClockMux0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1ClockMux0/McuClkMux0_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1ClockMux0/McuClockMux0_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1ClockMux0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm1PcsConfig_0</SHORT-NAME>
                          <INDEX>0</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1PcsConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1PcsConfig/McuClockPcfsUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1PcsConfig/McuPCS_MaxAllowableDynamicIDD</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1PcsConfig/McuPCS_Name</DEFINITION-REF>
                              <VALUE>PCFS_4</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm1SettingConfig/McuCgm1PcsConfig/McuPCS_SourceFrequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCgm2SettingConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuPCSStepDuration</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuPCSSwitchDuration</DEFINITION-REF>
                          <VALUE>48</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClkMux0Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClkMux0Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClkMux0Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClkMux0_Source</DEFINITION-REF>
                              <VALUE>ACCEL_PLL_PHI1_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClockMux0Divider0_Frequency</DEFINITION-REF>
                              <VALUE>6.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClockMux0_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClkMux1Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClkMux1Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClkMux1Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClkMux1_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI5_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClockMux1Divider0_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux1/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClkMux2Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClkMux2Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClkMux2Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClkMux2_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI5_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClockMux2Divider0_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux2/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClkMux3Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClkMux3Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClkMux3Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClkMux3_Source</DEFINITION-REF>
                              <VALUE>PERIPH_PLL_PHI5_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClockMux3Divider0_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClockMux3_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux3/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClkMux4Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClkMux4Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClkMux4_Source</DEFINITION-REF>
                              <VALUE>PFE_MAC_0_EXT_RX_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClockMux4Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClockMux4_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux4/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux5</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux5</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux5/McuClkMux5_Source</DEFINITION-REF>
                              <VALUE>PFE_MAC_1_EXT_RX_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux5/McuClockMux5_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux5/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux6</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux6</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux6/McuClkMux6_Source</DEFINITION-REF>
                              <VALUE>PFE_MAC_2_EXT_RX_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux6/McuClockMux6_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux6/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux7</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClkMux7Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClkMux7Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClkMux7Div0_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClkMux7_Source</DEFINITION-REF>
                              <VALUE>PFE_MAC_0_EXT_REF_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClockMux7Divider0_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClockMux7_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux7/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux8</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClkMux8Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClkMux8Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClkMux8Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClkMux8_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClockMux8Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClockMux8_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux8/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2ClockMux9</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClkMux9Div0Trigger</DEFINITION-REF>
                              <VALUE>COMMON_TRIGGER_DIVIDER_UPDATE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClkMux9Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClkMux9Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClkMux9_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClockMux9Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClockMux9_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2ClockMux9/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm2PcsConfig_0</SHORT-NAME>
                          <INDEX>0</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2PcsConfig</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2PcsConfig/McuClockPcfsUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2PcsConfig/McuPCS_MaxAllowableDynamicIDD</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2PcsConfig/McuPCS_Name</DEFINITION-REF>
                              <VALUE>PCFS_33</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuCgm2PcsConfig/McuPCS_SourceFrequency</DEFINITION-REF>
                              <VALUE>6.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuGENCTRL1_EMAC0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC0/McuGENCTRL1_EMAC0_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC0/McuGENCTRL1_EMAC0_Source</DEFINITION-REF>
                              <VALUE>SERDES_1_XPCS_0_TX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuGENCTRL1_EMAC1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC1/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC1/McuGENCTRL1_EMAC1_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC1/McuGENCTRL1_EMAC1_Source</DEFINITION-REF>
                              <VALUE>SERDES_1_XPCS_1_TX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuGENCTRL1_EMAC2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC2/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC2/McuGENCTRL1_EMAC2_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm2SettingConfig/McuGENCTRL1_EMAC2/McuGENCTRL1_EMAC2_Source</DEFINITION-REF>
                              <VALUE>SERDES_0_XPCS_1_TX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCgm5SettingConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm5SettingConfig</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm5ClockMux0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm5SettingConfig/McuCgm5ClockMux0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm5SettingConfig/McuCgm5ClockMux0/McuClkMux0_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm5SettingConfig/McuCgm5ClockMux0/McuClockMux0_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm5SettingConfig/McuCgm5ClockMux0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCgm6SettingConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm6ClockMux0</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0/McuClkMux0Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0/McuClkMux0Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0/McuClkMux0_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0/McuClockMux0Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux0/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm6ClockMux1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1/McuClkMux1Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1/McuClkMux1Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1/McuClkMux1_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1/McuClockMux1Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux1/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm6ClockMux2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux2/McuClkMux2_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux2/McuClockMux2_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux2/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuCgm6ClockMux3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClkMux3Div0_Divisor</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClkMux3Div0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClkMux3_Source</DEFINITION-REF>
                              <VALUE>FIRC_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClockMux3Divider0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClockMux3_Frequency</DEFINITION-REF>
                              <VALUE>4.8E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCgm6SettingConfig/McuCgm6ClockMux3/McuClockMuxUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_0</SHORT-NAME>
                      <INDEX>0</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_0_FXOSC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_1</SHORT-NAME>
                      <INDEX>1</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_5_XBAR_DIV3_FAIL_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_2</SHORT-NAME>
                      <INDEX>2</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_6_CORE_M7_0_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_3</SHORT-NAME>
                      <INDEX>3</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_7_XBAR_DIV3_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_4</SHORT-NAME>
                      <INDEX>4</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_8_CORE_M7_1_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_5</SHORT-NAME>
                      <INDEX>5</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_9_CORE_M7_2_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_6</SHORT-NAME>
                      <INDEX>6</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_10_PER_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_7</SHORT-NAME>
                      <INDEX>7</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_11_SERDES_REF_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_8</SHORT-NAME>
                      <INDEX>8</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_12_FLEXRAY_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_9</SHORT-NAME>
                      <INDEX>9</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_13_FLEXCAN_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_10</SHORT-NAME>
                      <INDEX>10</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_14_GMAC0_TX_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_11</SHORT-NAME>
                      <INDEX>11</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_15_GMAC_TS_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_12</SHORT-NAME>
                      <INDEX>12</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_16_LINFLEXD_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_13</SHORT-NAME>
                      <INDEX>13</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_17_QSPI_1X_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_14</SHORT-NAME>
                      <INDEX>14</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_18_SDHC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_15</SHORT-NAME>
                      <INDEX>15</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_20_DDR_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_16</SHORT-NAME>
                      <INDEX>16</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_21_GMAC0_RX_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_17</SHORT-NAME>
                      <INDEX>17</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_22_SPI_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_18</SHORT-NAME>
                      <INDEX>18</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_24_CORE_M7_3_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_19</SHORT-NAME>
                      <INDEX>19</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_27_CORE_A53_CLUSTER_0_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_20</SHORT-NAME>
                      <INDEX>20</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_28_CORE_A53_CLUSTER_1_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_21</SHORT-NAME>
                      <INDEX>21</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_39_PFE_SYS_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_22</SHORT-NAME>
                      <INDEX>22</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_46_PFEMAC0_TX_DIV_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_23</SHORT-NAME>
                      <INDEX>23</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_47_PFEMAC0_RX_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_24</SHORT-NAME>
                      <INDEX>24</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_48_PFEMAC1_TX_DIV_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_25</SHORT-NAME>
                      <INDEX>25</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_49_PFEMAC1_RX_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_26</SHORT-NAME>
                      <INDEX>26</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_50_PFEMAC2_TX_DIV_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuClkMonitor_27</SHORT-NAME>
                      <INDEX>27</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuAsyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClkMonitorEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuClockMonitorUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuCmuName</DEFINITION-REF>
                          <VALUE>CMU_FC_51_PFEMAC2_RX_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFHHInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClkMonitor/McuSyncFLLInterruptEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>M7_CLK_REF</SHORT-NAME>
                      <INDEX>0</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockFrequencySelect</DEFINITION-REF>
                          <VALUE>XBAR_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>4.0E8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CAN_PE_CLK</SHORT-NAME>
                      <INDEX>1</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockFrequencySelect</DEFINITION-REF>
                          <VALUE>CAN_PE_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>4.0E7</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>LLCE_SUBSYS</SHORT-NAME>
                      <INDEX>2</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockFrequencySelect</DEFINITION-REF>
                          <VALUE>XBAR_DIV2_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>2.0E8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>LIN_PE_CLK</SHORT-NAME>
                      <INDEX>3</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockFrequencySelect</DEFINITION-REF>
                          <VALUE>LIN_BAUD_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>4.8E7</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>UART_CLK</SHORT-NAME>
                      <INDEX>4</INDEX>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockFrequencySelect</DEFINITION-REF>
                          <VALUE>LIN_BAUD_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>4.8E7</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuCoreDfs</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>8.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_1/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_2/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_3/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_4/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_5</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_5/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_6</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuCoreDfs/McuDfs_6/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuFXOSC</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFXOSC_Frequency</DEFINITION-REF>
                          <VALUE>4.0E7</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscALCEnable</DEFINITION-REF>
                          <VALUE>Enabled</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscByPass</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscCounter</DEFINITION-REF>
                          <VALUE>157</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscMainComparator</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscOverdriveProtection</DEFINITION-REF>
                          <VALUE>12</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscPowerDownCtr</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuFXOSC/McuFxoscUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPeriphDfs</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>8.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_1/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_2/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_3/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_4/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_5</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_5/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuDfs_6</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6/DFS_CLK_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6/McuDFSPortMfi</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6/McuDFSPortMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6/McuDFSPort_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPeriphDfs/McuDfs_6/McuDFSUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPll_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPLLEnabled</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPLLUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPllClockSelection</DEFINITION-REF>
                          <VALUE>FXOSC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Configuration</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllDvMfi</DEFINITION-REF>
                              <VALUE>40</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllDvRdiv</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFdEmdp</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFdFmod</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFdMdp</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFdMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFdSdmen</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFmSpreadctl</DEFINITION-REF>
                              <VALUE>Center_Spread</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFmSscgbyp</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFmStepNo</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllFmStepSize</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllOdiv0_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllOdiv0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllOdiv1_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Configuration/McuPllOdiv1_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Parameter</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Parameter</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Parameter/PLL_PHI0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Parameter/PLL_PHI1_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_0/McuPll_Parameter/PLL_VCO_Frequency</DEFINITION-REF>
                              <VALUE>1.6E9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPll_1</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPLLEnabled</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPLLUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPllClockSelection</DEFINITION-REF>
                          <VALUE>FXOSC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Configuration</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllDvMfi</DEFINITION-REF>
                              <VALUE>50</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllDvRdiv</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllFdMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllFdSdmen</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv0_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv1_Div</DEFINITION-REF>
                              <VALUE>31</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv1_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv2_Div</DEFINITION-REF>
                              <VALUE>49</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv2_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv3_Div</DEFINITION-REF>
                              <VALUE>49</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv3_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv4_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv4_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv5_Div</DEFINITION-REF>
                              <VALUE>15</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv5_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv6_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv6_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv7_Div</DEFINITION-REF>
                              <VALUE>39</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Configuration/McuPllOdiv7_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Parameter</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI1_Frequency</DEFINITION-REF>
                              <VALUE>6.25E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI2_Frequency</DEFINITION-REF>
                              <VALUE>4.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI3_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI4_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI5_Frequency</DEFINITION-REF>
                              <VALUE>1.25E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI6_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_PHI7_Frequency</DEFINITION-REF>
                              <VALUE>5.0E7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_1/McuPll_Parameter/PLL_VCO_Frequency</DEFINITION-REF>
                              <VALUE>2.0E9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPll_2</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPLLEnabled</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPLLUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPllClockSelection</DEFINITION-REF>
                          <VALUE>FXOSC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Configuration</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllDvMfi</DEFINITION-REF>
                              <VALUE>60</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllDvRdiv</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFdEmdp</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFdFmod</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFdMdp</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFdMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFdSdmen</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFmSpreadctl</DEFINITION-REF>
                              <VALUE>Center_Spread</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFmSscgbyp</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFmStepNo</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllFmStepSize</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllOdiv0_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllOdiv0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllOdiv1_Div</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Configuration/McuPllOdiv1_En</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Parameter</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Parameter</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Parameter/PLL_PHI0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Parameter/PLL_PHI1_Frequency</DEFINITION-REF>
                              <VALUE>6.0E8</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_2/McuPll_Parameter/PLL_VCO_Frequency</DEFINITION-REF>
                              <VALUE>2.4E9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPll_3</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPLLEnabled</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPLLUnderMcuControl</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPllClockSelection</DEFINITION-REF>
                          <VALUE>FXOSC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Configuration</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllDvMfi</DEFINITION-REF>
                              <VALUE>50</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllDvRdiv</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFdEmdp</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFdFmod</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFdMdp</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFdMfn</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFdSdmen</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFmSpreadctl</DEFINITION-REF>
                              <VALUE>Center_Spread</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFmSscgbyp</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFmStepNo</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllFmStepSize</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllOdiv0_Div</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Configuration/McuPllOdiv0_En</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPll_Parameter</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Parameter</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Parameter/PLL_PHI0_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPll_3/McuPll_Parameter/PLL_VCO_Frequency</DEFINITION-REF>
                              <VALUE>0.0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuRtcClockSelect</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuRtcClockSelect</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuRtcClockSelect/McuClockMuxUnderMcuControl</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuRtcClockSelect/McuRtc_Frequency</DEFINITION-REF>
                          <VALUE>4.8E7</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuRtcClockSelect/McuRtc_Source</DEFINITION-REF>
                          <VALUE>FIRC_CLK</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>McuModeSettingConf_0</SHORT-NAME>
                  <INDEX>0</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuEnableSleepOnExit</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuMainCoreSelect</DEFINITION-REF>
                      <VALUE>HSE_CM7</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuMode</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPowerMode</DEFINITION-REF>
                      <VALUE>RUN</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPartitionConfiguration</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition0Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPrstCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuPrtnCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore0Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore0Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore1Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore1Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore2Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore2Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore4Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition0Config/McuCore4Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition1Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPrstCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuPrtnCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore0Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore0Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore1Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore1Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore2Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore2Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore3Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore3Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore4Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore4Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore5Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore5Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore6Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore6Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>McuCore7Configuration</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration/McuCoreBootAddress</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration/McuCoreBootAddressLinkerSym</DEFINITION-REF>
                                  <VALUE></VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration/McuCoreClockEnable</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration/McuCoreResetEnable</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition1Config/McuCore7Configuration/McuCoreUnderMcuControl</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition2Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPrstCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition2Config/McuPrtnCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition3Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPrstCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition3Config/McuPrtnCofb0UnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition4Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition4Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition4Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition4Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition4Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition4Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition5Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition5Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition5Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition5Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition5Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition5Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition6Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition6Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition6Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition6Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition6Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition6Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPartition7Config</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition7Config</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition7Config/McuPartitionClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition7Config/McuPartitionPowerUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition7Config/McuPartitionResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPartition7Config/McuPartitionUnderMcuControl</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_0</SHORT-NAME>
                          <INDEX>0</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN0_COFB0_REQ0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>uSDHC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_1</SHORT-NAME>
                          <INDEX>1</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN0_COFB0_REQ1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>DDR_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>PRST0_COFB0_PERIPH_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_2</SHORT-NAME>
                          <INDEX>2</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PCIe_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>PRST0_COFB0_PERIPH_4</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_3</SHORT-NAME>
                          <INDEX>3</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PCIe_0_CSS</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>PRST0_COFB0_PERIPH_5</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_4</SHORT-NAME>
                          <INDEX>4</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PCIe_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>PRST0_COFB0_PERIPH_16</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_5</SHORT-NAME>
                          <INDEX>5</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PCIe_1_CSS</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>PRST0_COFB0_PERIPH_17</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_6</SHORT-NAME>
                          <INDEX>6</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN2_COFB0_REQ0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PFE_MAC0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_7</SHORT-NAME>
                          <INDEX>7</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN2_COFB0_REQ1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PFE_MAC1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_8</SHORT-NAME>
                          <INDEX>8</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN2_COFB0_REQ2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PFE_MAC2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuPeripheral_9</SHORT-NAME>
                          <INDEX>9</INDEX>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuModeEntrySlot</DEFINITION-REF>
                              <VALUE>PRTN2_COFB0_REQ3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralClockEnable</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralName</DEFINITION-REF>
                              <VALUE>PFE_TS_CLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuPeripheralResetEnable</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuModeSettingConf/McuPartitionConfiguration/McuPeripheral/McuResetGenerationSlot</DEFINITION-REF>
                              <VALUE>NONE</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>McuPowerControl</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuPMC_Config</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuPADS_CLKOUTNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_ADC0NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_ADC1NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_EFUSENonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_FXOSCNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_HV_PLLNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_HV_PLL_ACCNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_HV_PLL_AURNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_HV_PLL_DDR0NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_ANonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_A_GPIO1NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_BNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_B_GPIO2NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_B_GPIO3NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_CLKOUTNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_C_GPIO4NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_GMAC0NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_GMAC1NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_QSPINonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_SDHCNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_STBYNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_IO_USBNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_ACC_PLL_30NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_ACC_PLL_31NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_PERIPH_PLLNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_PLLNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_PLL_ACCNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_PLL_AURNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_LV_PLL_DDR0NonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuPowerControl/McuPMC_Config/McuVDD_TMUNonCriticalFlag</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>McuResetConfig</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuDestResetEscThreshold</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuFuncResetEscThreshold</DEFINITION-REF>
                      <VALUE>15</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetType</DEFINITION-REF>
                      <VALUE>FunctionalReset</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>McuResetSourcesConfig</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetSourcesConfig</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuEXR_ResetSource</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetSourcesConfig/McuEXR_ResetSource</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetSourcesConfig/McuEXR_ResetSource/McuDisableReset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>McuF_FR_31_ResetSource</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetSourcesConfig/McuF_FR_31_ResetSource</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuModuleConfiguration/McuResetConfig/McuResetSourcesConfig/McuF_FR_31_ResetSource/McuDisableReset</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_ACC_LOL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>9</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_CORE_LOL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_DDR_LOL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>8</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_DEBUG_DEST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>15</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_DEBUG_FUNC_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>24</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_EXT_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>16</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_FCCU_FTR_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_FCCU_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>17</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_FXOSC_FAIL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_BOOT_ERR_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>21</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_CORE_LOCK_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>22</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_LC_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>11</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_RAM_ECC_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>20</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_SNVS_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>12</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_HSE_SWT_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>13</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_MC_RGM_FRE_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_MULTIPLE_RESET_REASON</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>27</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_NC_SPD_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_NO_RESET_REASON</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>26</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_PERIPH_LOL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_POWER_ON_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_RESET_UNDEFINED</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>28</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_STCU_URF_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_ST_DONE_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>18</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_SWT0_RST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>19</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_SW_DEST_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>14</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_SW_FUNC_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>23</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_WAKEUP_REASON</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>25</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>MCU_XBAR_DIV3_CLK_FAIL_RESET</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>10</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>McuScmiApiConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuGetClockApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuGetClockStatusApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuPerformDomainPeriphResetApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuPowerStateGetApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuPowerStateSetApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuSetAllCoresBootAddressApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuSetClockConfigApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuSetClockFrequencyApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M50I0R0/Mcu/McuScmiApiConfiguration/McuSetCoreBootAddressApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
