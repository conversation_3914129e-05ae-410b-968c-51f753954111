<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Llce_Af</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Llce_Af</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M10I10R0/Llce_Af</DEFINITION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>99</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>10</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>43</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>LlceAfGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/EnableAdvancedFeatureMode</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Can2CanRoutingTable_Can2CanFd</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/Can2CanFd</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanFd2Can</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CanDestinationList_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList/Can2CanControllerRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Can2CanRoutingTable_Can2CanFd_CanIdRemap</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/Can2CanFd</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanFd2Can</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanIdRemapping</DEFINITION-REF>
                      <VALUE>999</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CanDestinationList_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList/Can2CanControllerRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Can2CanRoutingTable_CanFd2Can</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/Can2CanFd</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanFd2Can</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CanDestinationList_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList/Can2CanControllerRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Can2CanRoutingTable_CanIdRemap</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/Can2CanFd</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanFd2Can</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanIdRemapping</DEFINITION-REF>
                      <VALUE>16</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CanDestinationList_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList/Can2CanControllerRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Can2CanRoutingTable_PassThrough</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/Can2CanFd</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanFd2Can</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>CanDestinationList_0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Can2CanRoutingTable/CanDestinationList/Can2CanControllerRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_Can2CanFd</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>Can2CanRoutingEnable</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable/Can2CanRoutingTableRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_Can2CanFd</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_Can2CanFd_CanIdRemap</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>Can2CanRoutingEnable</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable/Can2CanRoutingTableRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_Can2CanFd_CanIdRemap</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_Can2Eth_AVTP_NTSCF_BRIEF</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_Can2Eth_UDP</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_CanFd2Can</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>Can2CanRoutingEnable</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable/Can2CanRoutingTableRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_CanFd2Can</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_CanIdRemap</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>Can2CanRoutingEnable</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable/Can2CanRoutingTableRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_CanIdRemap</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>CanAdvancedFeature_PassThrough</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/AuthenticationFrameEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/CustomProcessingIndex</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/HostReceiveEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/LoggingFeatureEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>Can2CanRoutingEnable</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/CanAdvancedFeature/Can2CanRoutingEnable/Can2CanRoutingTableRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Llce_Af/Llce_Af/LlceAfGeneral/Can2CanRoutingTable_PassThrough</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>Eth2Can</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Eth2Can</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TS_T40D11M10I10R0/Llce_Af/LlceAfGeneral/Eth2Can/Eth2CanEnable</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
