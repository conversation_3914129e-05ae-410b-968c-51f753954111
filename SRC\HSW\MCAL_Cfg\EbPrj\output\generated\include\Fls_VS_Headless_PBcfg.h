/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : IPV_QSPI
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef FLS_VS_HEADLESS_PBCFG_H
#define FLS_VS_HEADLESS_PBCFG_H

/**
 *   @file       Fls_VS_Headless_PBcfg.h
 *
 *   @addtogroup FLS
 *   @implements Fls_PBcfg.h_Artifact
 *   @{
 */

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/


/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_VS_HEADLESS_PBCFG_VENDOR_ID                    43

#define FLS_VS_HEADLESS_PBCFG_AR_RELEASE_MAJOR_VERSION     4
#define FLS_VS_HEADLESS_PBCFG_AR_RELEASE_MINOR_VERSION     4
#define FLS_VS_HEADLESS_PBCFG_AR_RELEASE_REVISION_VERSION  0

#define FLS_VS_HEADLESS_PBCFG_SW_MAJOR_VERSION             5
#define FLS_VS_HEADLESS_PBCFG_SW_MINOR_VERSION             0
#define FLS_VS_HEADLESS_PBCFG_SW_PATCH_VERSION             0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/


/*==================================================================================================
*                                         CONSTANTS
==================================================================================================*/

#define FLS_CONFIG_VS_HEADLESS_PB \
    extern const Fls_ConfigType Fls_Config_VS_Headless;

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/* Number of flash devices using SFDP */
#define FLS_DEVICES_USING_SFDP    0U

/*==================================================================================================
*                                            ENUMS
==================================================================================================*/


/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/


/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/**@}*/

#endif    /* #ifndef FLS_VS_HEADLESS_PBCFG_H */

