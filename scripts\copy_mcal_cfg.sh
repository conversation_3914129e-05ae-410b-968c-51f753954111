#!/bin/bash

# Script to copy YL2_Config_G3 into build dir

SOURCE_DIR="../YL2_Config_G3"
BASE_DIR="SRC/HSW/MCAL_Cfg"

# Check if the base directory exists
if [ ! -d "$BASE_DIR" ]; then
    echo "Directory '$BASE_DIR' does not exist, try to mkdir."
    mkdir -p "$BASE_DIR"
fi

echo Copying $SOURCE_DIR recursively to SRC/HSW/ ...

# Copy all files and dirs from SOURCE_DIR to BASE_DIR
cp -r "$SOURCE_DIR"/* "$BASE_DIR"

echo Done.

