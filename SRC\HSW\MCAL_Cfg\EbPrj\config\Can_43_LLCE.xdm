<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Can_43_LLCE" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Can" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/Can_43_LLCE_TS_T40D11M10I10R0/Can"/>
              <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="CanGeneral" type="IDENTIFIABLE">
                <d:var name="CanDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="CanEnableUserModeSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="CanMulticoreSupport" type="BOOLEAN" value="true"/>
                <d:var name="HostInterface" type="ENUMERATION" 
                       value="LLCE_CAN_HIF0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionBusoffPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CanMainFunctionWakeupPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionModePeriod" type="FLOAT" 
                       value="0.005"/>
                <d:var name="CanMultiplexedTransmission" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_SYSTEM"/>
                <d:var name="CanTimeoutDuration" type="FLOAT" value="0.01"/>
                <d:var name="CanLPduReceiveCalloutFunction" 
                       type="FUNCTION-NAME" value="CanNm_WakeUpCheck">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="LPDUCalloutExtension" type="BOOLEAN" value="false">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="RxTimestampNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="TxTimestampNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:lst name="CanEcucPartitionRef">
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                    <a:a name="VARIANTS" type="Variant">
                      <variant:pbvcond>
                        <variant:criterion 
                                           value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                        <variant:cond>
                          <variant:tst expr="0"/>
                        </variant:cond>
                      </variant:pbvcond>
                    </a:a>
                  </d:ref>
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                    <a:a name="VARIANTS" type="Variant">
                      <variant:pbvcond>
                        <variant:criterion 
                                           value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                        <variant:cond>
                          <variant:tst expr="0"/>
                        </variant:cond>
                      </variant:pbvcond>
                    </a:a>
                  </d:ref>
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                    <a:a name="VARIANTS" type="Variant">
                      <variant:pbvcond>
                        <variant:criterion 
                                           value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                        <variant:cond>
                          <variant:tst expr="1"/>
                        </variant:cond>
                      </variant:pbvcond>
                    </a:a>
                  </d:ref>
                  <d:ref type="REFERENCE" 
                         value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                    <a:a name="VARIANTS" type="Variant">
                      <variant:pbvcond>
                        <variant:criterion 
                                           value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                        <variant:cond>
                          <variant:tst expr="1"/>
                        </variant:cond>
                      </variant:pbvcond>
                    </a:a>
                  </d:ref>
                </d:lst>
                <d:ref name="CanOsCounterRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="true"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ref name="CanSupportTTCANRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:var name="CanSetBaudrateApi" type="BOOLEAN" value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanApiEnableMbAbort" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanAbortOnlyOneMB" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanLlceEnableHeadlessMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanLlceHeadlessModeUsed" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CanLlceControllerStartNoSync" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanCustomRxFunction" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CanWriteCustomCallback" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CanTxConfirmationCustomCallback" 
                       type="FUNCTION-NAME" value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanAuxFilterEnable" type="BOOLEAN" value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanAuxFilterCallback" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="BusOffNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="CanMainFunctionRWPeriods" type="MAP">
                  <d:ctr name="CanMainFunctionRWPeriods5ms" type="IDENTIFIABLE">
                    <d:var name="CanMainFunctionPeriod" type="FLOAT" 
                           value="0.005"/>
                    <d:var name="AckInterface" type="INTEGER" value="16">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="RxInterface" type="INTEGER" value="16">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:var name="CanPublicIcomSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="CanIcomGeneral" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanIcomLevel" type="ENUMERATION" 
                         value="CAN_ICOM_LEVEL_ONE">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanIcomVariant" type="ENUMERATION" 
                         value="CAN_ICOM_VARIANT_NONE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CanConfigSet" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                <d:ctr name="CanErrorReporting" type="IDENTIFIABLE">
                  <d:var name="CAN_PROTOCOL_ERR" type="ENUMERATION" 
                         value="INTERRUPT">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CAN_PROTOCOL_WRN" type="ENUMERATION" 
                         value="INTERRUPT">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="DATALOST_ERR" type="ENUMERATION" 
                         value="INTERRUPT">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CONFIGURATION_ERR" type="ENUMERATION" 
                         value="IGNORE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="INTERNAL_ERR" type="ENUMERATION" 
                         value="INTERRUPT">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanErrorNotif" type="FUNCTION-NAME" 
                         value="NULL_PTR">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanMainFunctionErrorEn" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:lst name="CanController" type="MAP">
                  <d:ctr name="CAN0_LlceCan0" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="true">
                          <a:a name="ENABLE" value="false"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN0_LlceCan0/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:var>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN1_LlceCan1" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_1"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1/CanControllerBaudrateConfig"/>
                    <d:var name="CanControllerId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:var>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN3_LlceCan3" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_3"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN3_LlceCan3/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="2"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN4_LlceCan4" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_4"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN4_LlceCan4/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="3"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN5_LlceCan5" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_5"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN5_LlceCan5/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="4"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN6_LlceCan6" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_6"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN6_LlceCan6/CanControllerBaudrateConfig"/>
                    <d:var name="CanControllerId" type="INTEGER" value="5"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN7_LlceCan7" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_7"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN7_LlceCan7/CanControllerBaudrateConfig"/>
                    <d:var name="CanControllerId" type="INTEGER" value="6"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN8_LlceCan8" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_8"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN8_LlceCan8/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="7"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN9_LlceCan9" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" value="BCAN_9"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN9_LlceCan9/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="8"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN10_LlceCan10" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="BCAN_10"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN10_LlceCan10/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="9"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN11_LlceCan11" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="BCAN_11"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN11_LlceCan11/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="10"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN12_LlceCan12" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="BCAN_12"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN12_LlceCan12/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="11"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN13_LlceCan13" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="BCAN_13"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN13_LlceCan13/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="12"/>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CAN15_LlceCan15" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="BCAN_15"/>
                    <d:var name="RWDestInterface" type="ENUMERATION" 
                           value="FIFO_INTERFACE_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerIcomSupported" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="ENUMERATION" 
                           value="END_OF_FRAME"/>
                    <d:var name="CanProtocolException" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanListenOnlyMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="SelfReceptionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTxFifoMode" type="BOOLEAN" value="true"/>
                    <d:var name="AutomaticBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="ManualBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection/EcucPartition_1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/CAN_PE_CLK"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                      <d:ctr name="CanControllerBaudrateConfig" 
                             type="IDENTIFIABLE">
                        <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerCheckCanStandard" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="1"/>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="47"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" 
                               value="16"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="16"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanAdvancedSettingFd" type="BOOLEAN" 
                                 value="false">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanBusLengthFd" type="INTEGER" value="40">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerFDPrescaller" 
                                 type="INTEGER" value="1"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="7"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="6"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="6"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="0">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN15_LlceCan15/CanControllerBaudrateConfig">
                      <a:a name="IMPORTER_INFO" value="ImportEcuConfig"/>
                    </d:ref>
                    <d:var name="CanControllerId" type="INTEGER" value="13">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="CanHardwareObject" type="MAP">
                  <d:ctr name="Rx_CCU_CANFD1" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN0_LlceCan0"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="0"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD1" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN0_LlceCan0"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="14"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="97"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" 
                             value=""/>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD2" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="1"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" 
                             value=""/>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD2" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN1_LlceCan1"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="15"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="272"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" 
                             value=""/>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD3" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN3_LlceCan3"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="2"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD3" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN3_LlceCan3"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="16"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="101"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD4" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN4_LlceCan4"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="3"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD4" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN4_LlceCan4"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="17"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="103"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD5" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN5_LlceCan5"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="4"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD5" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN5_LlceCan5"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="18"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="105"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD6" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN6_LlceCan6"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="5"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD6" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN6_LlceCan6"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="19"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="105"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD7" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN7_LlceCan7"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="6"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD7" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN7_LlceCan7"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="20"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="105"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD8" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN8_LlceCan8"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="7"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD8" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN8_LlceCan8"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="21"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="107"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD9" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN9_LlceCan9"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="8"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD9" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN9_LlceCan9"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="22"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="109"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD10" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN10_LlceCan10"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="9"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD10" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN10_LlceCan10"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="23"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="111"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD11" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN11_LlceCan11"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="10"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD11" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN11_LlceCan11"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="24"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="113"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD12" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN12_LlceCan12"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="11"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD12" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN12_LlceCan12"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="25"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="121"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD13" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN13_LlceCan13"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="12"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD13" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN13_LlceCan13"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="26"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="123"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Rx_CCU_CANFD14" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN15_LlceCan15"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="13"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="64"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="0"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="Tx_CCU_CANFD14" type="IDENTIFIABLE">
                    <d:var name="Dummy_HRH" type="BOOLEAN" value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="MIXED"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanTxAddFrameMac" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanLlceKeyHandle" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can_43_LLCE/Can/CanConfigSet/CAN15_LlceCan15"/>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC"/>
                    <d:var name="CanObjectId" type="INTEGER" value="27"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="10"/>
                    <d:var name="CanObjectPayloadLength" type="ENUMERATION" 
                           value="CAN_OBJECT_PL_64">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="CanHwFilter" type="IDENTIFIABLE">
                      <d:var name="CanHwFilterCode" type="INTEGER" value="127"/>
                      <d:var name="CanHwFilterMask" type="INTEGER" value="0"/>
                    </d:ctr>
                    <d:ctr name="RangeFilter" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="RangeStart" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="RangeEnd" type="INTEGER" value="4294967295">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="CanAdvancedFeature" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="CanAdvancedFeatureRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                </d:lst>
                <d:lst name="CanAuxFilter" type="MAP"/>
                <d:ctr name="CanIcom" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:lst name="CanIcomConfig" type="MAP"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="80">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="10"/>
                <d:var name="VendorApiInfix" type="STRING" value="LLCE">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
