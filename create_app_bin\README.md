## Create APP Bin

1. `CORTEXM_S32G399_hpe_bootrom.blob`为BOOT镜像，需要烧录到QSpi Flash的0x00000000地址
1. 调用`create_app_blob.py create_app_blob_cfg.json`命令，可以生成APP镜像文件`CORTEXM_S32G399_MCAL.blob`。
1. 按照当前CORTEXM_S32G399_hpe_bootrom.blob配置，APP镜像需要烧录到0x00200000地址。
1. `create_app_blob_cfg.json`为构造APP镜像的配置文件，需要配置以下信息：
	- 编译生成的app bin文件路径：配置`"img_file"`和`"img_size"`下的`"path"`值
	- 脚本生成的烧录文件名称：配置`"output_name"`信息
	- 脚本生成的烧录文件路径：配置`"output_path"`信息
	- APP需要加载到的RAM起始地址：配置`"img_ram_start_addr"`下的`"value"`信息
	- APP的entrypoint：配置`"img_entrypoint"`下的"value"信息
1. 注意"img_file"下的 "path"信息以及"img_size"下的"path"信息需要相同
1. 脚本生成的烧录文件可以直接烧录到QSPI Flash。