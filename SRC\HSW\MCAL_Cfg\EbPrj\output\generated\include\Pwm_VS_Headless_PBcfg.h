/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : Ftm
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef PWM_VS_HEADLESS_PB_CFG_H
#define PWM_VS_HEADLESS_PB_CFG_H

/**
*   @file       Pwm_VS_Headless_PBcfg.h
*
*   @addtogroup pwm_driver Pwm Driver
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                                          INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "StandardTypes.h"

/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_VS_HEADLESS_PB_CFG_VENDOR_ID                    43
#define PWM_VS_HEADLESS_PB_CFG_MODULE_ID                    121
#define PWM_VS_HEADLESS_PB_CFG_AR_RELEASE_MAJOR_VERSION     4
#define PWM_VS_HEADLESS_PB_CFG_AR_RELEASE_MINOR_VERSION     4
#define PWM_VS_HEADLESS_PB_CFG_AR_RELEASE_REVISION_VERSION  0
#define PWM_VS_HEADLESS_PB_CFG_SW_MAJOR_VERSION             5
#define PWM_VS_HEADLESS_PB_CFG_SW_MINOR_VERSION             0
#define PWM_VS_HEADLESS_PB_CFG_SW_PATCH_VERSION             0

/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if source file and StandardTypes.h are of the same AUTOSAR version */
    #if ((PWM_VS_HEADLESS_PB_CFG_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
         (PWM_VS_HEADLESS_PB_CFG_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR version numbers of Pwm_VS_Headless_PBcfg.h and StandardTypes.h are different."
    #endif
#endif

/*==================================================================================================
*                                            CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       DEFINES AND MACROS
==================================================================================================*/
/**
* @brief        Total number of configured PWM channels
*/
#define PWM_VS_HEADLESS_PB_CFG_CHANNELS_COUNT           ((uint8)1U)

/**
* @brief        Total number of configured PWM instances
*/
#define PWM_VS_HEADLESS_PB_CFG_INSTANCES_COUNT          ((uint8)1U)

/*==================================================================================================
*                                              ENUMS
==================================================================================================*/

/*==================================================================================================
*                                  STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                  GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

#define PWM_CONFIG_VS_HEADLESS_PB \
        extern const Pwm_ConfigType Pwm_Config_VS_Headless;

#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/*==================================================================================================
*                                       FUNCTION PROTOTYPES
==================================================================================================*/
#define PWM_START_SEC_CODE
#include "Pwm_MemMap.h"


#define PWM_STOP_SEC_CODE
#include "Pwm_MemMap.h"


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PWM_VS_HEADLESS_PB_CFG_H */

