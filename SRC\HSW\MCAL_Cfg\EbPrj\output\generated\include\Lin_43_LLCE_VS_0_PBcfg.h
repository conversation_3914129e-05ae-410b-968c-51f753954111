/**
*   @file    Lin_43_LLCE_VS_0_PBcfg.h
*   @version 1.0.10
*
*   @brief   AUTOSAR Lin_43_LLCE - High level header of LIN driver.
*   @details This file contains declarations of the functions defined by AutoSAR.
*
*   @addtogroup LIN_LLCE
*   @{
*/
/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : LLCE
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 1.0.10
*   Build Version        : S32_RTD_1_0_10_D2505_ASR_REL_4_4_REV_0000_20250516
*
*   Copyright 2006-2016 Freescale Semiconductor, Inc.
*   Copyright 2020-2025 NXP
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be used strictly in
*   accordance with the applicable license terms.
*   By expressly accepting such terms or by downloading, installing, activating and/or otherwise using
*   the software, you are agreeing that you have read, and that you agree to comply with and are bound
*   by, such license terms.  If you do not agree to be bound by the applicable license terms, then you
*   may not retain, install, activate or otherwise use the software.
==================================================================================================*/

#ifndef LIN_43_LLCE_VS_0_PBCFG_H
#define LIN_43_LLCE_VS_0_PBCFG_H

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @file           Lin_43_LLCE_VS_0_PBcfg.h
*/
/* Inclusion of incompatible header files shall be avoided */
#define LIN_43_LLCE_VS_0_PBCFG_VENDOR_ID                     43
#define LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_MAJOR_VERSION      4
#define LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_MINOR_VERSION      4
#define LIN_43_LLCE_VS_0_PBCFG_AR_RELEASE_REVISION_VERSION   0
#define LIN_43_LLCE_VS_0_PBCFG_SW_MAJOR_VERSION              1
#define LIN_43_LLCE_VS_0_PBCFG_SW_MINOR_VERSION              0
#define LIN_43_LLCE_VS_0_PBCFG_SW_PATCH_VERSION              10
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
#define LIN_43_LLCE_CONFIG_VS_0_PB \
extern const Lin_43_LLCE_ConfigType Lin_43_LLCE_Config_VS_0;
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/


/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif /* LIN_43_LLCE_VS_0_PBCFG_H */

/** @} */
